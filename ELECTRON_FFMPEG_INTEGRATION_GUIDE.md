# Electron FFmpeg.js 集成完整指南

## 项目概述

本项目基于 ElectronEgg 框架，集成了 Vue 3 前端和 FFmpeg.js 视频处理功能。通过本指南的配置，可以在 Electron 桌面应用中正常使用 FFmpeg.js 进行视频处理。

## 项目结构

```
├── electron/                          # Electron 主进程目录
│   ├── main.js                        # ElectronEgg 主入口
│   ├── preload/                       # 预加载脚本目录
│   │   ├── index.js                   # 预加载模块入口（已修改）
│   │   ├── bridge.js                  # 上下文桥接（已修改）
│   │   └── lifecycle.js               # 生命周期管理（已修改）
│   ├── service/                       # 服务目录
│   │   └── ffmpeg_security.js         # FFmpeg 安全配置服务（新增）
│   └── controller/                    # 控制器目录
│       └── file_dialog.js             # 文件对话框控制器（新增）
├── frontend/                          # Vue 3 前端目录
│   ├── src/
│   │   ├── utils/
│   │   │   ├── ffmpegHelper.js        # FFmpeg 工具类（已修改）
│   │   │   └── electronFileHandler.js # Electron 文件处理工具（新增）
│   │   └── components/
│   │       └── ElectronFFmpegTest.vue # 测试组件（新增）
│   └── package.json                   # 前端依赖配置
└── README.md                          # 项目说明
```

## 核心修改内容

### 1. FFmpeg Helper 增强 (`frontend/src/utils/ffmpegHelper.js`)

**主要功能**：
- ✅ Electron 环境自动检测
- ✅ SharedArrayBuffer polyfill 实现
- ✅ 分环境初始化策略
- ✅ 增强的错误处理和兼容性检查
- ✅ 多 CDN 源支持

**关键特性**：
```javascript
// 环境检测
detectElectronEnvironment() {
  return !!(window.electron || window.electronAPI || 
           navigator.userAgent.includes('electron'))
}

// SharedArrayBuffer polyfill
setupSharedArrayBufferPolyfill() {
  if (typeof SharedArrayBuffer === 'undefined') {
    window.SharedArrayBuffer = class extends ArrayBuffer { /* ... */ }
  }
}
```

### 2. Electron 安全配置服务 (`electron/service/ffmpeg_security.js`)

**主要功能**：
- ✅ 命令行参数配置（启用 SharedArrayBuffer）
- ✅ 响应头设置（CORS 和安全策略）
- ✅ 会话安全配置
- ✅ 环境变量设置

**关键配置**：
```javascript
// 命令行参数
app.commandLine.appendSwitch('enable-features', 'SharedArrayBuffer')
app.commandLine.appendSwitch('cross-origin-isolated')

// 响应头
responseHeaders['Cross-Origin-Opener-Policy'] = ['same-origin']
responseHeaders['Cross-Origin-Embedder-Policy'] = ['require-corp']
```

### 3. 预加载脚本增强 (`electron/preload/bridge.js`)

**主要功能**：
- ✅ SharedArrayBuffer polyfill 预设置
- ✅ 环境信息暴露
- ✅ FFmpeg 支持状态检测

### 4. 文件处理工具 (`frontend/src/utils/electronFileHandler.js`)

**主要功能**：
- ✅ 统一的文件选择接口
- ✅ Electron 和浏览器环境兼容
- ✅ 原生文件对话框支持
- ✅ 文件预览 URL 处理

## 安装和配置

### 1. 安装依赖

```bash
# 安装前端依赖
cd frontend
npm install

# 安装 Electron 依赖（如果需要）
cd ../electron
npm install
```

### 2. 配置 IPC 路由

在 ElectronEgg 的路由配置中添加文件对话框路由：

```javascript
// electron/config/config.default.js 或相应配置文件
module.exports = {
  // ... 其他配置
  ipc: {
    // 添加文件对话框路由
    'dialog:showOpenDialog': 'file_dialog.showOpenDialog',
    'dialog:showSaveDialog': 'file_dialog.showSaveDialog',
    'fs:readFile': 'file_dialog.readFile',
    'fs:writeFile': 'file_dialog.writeFile',
    'fs:getFileInfo': 'file_dialog.getFileInfo',
    'fs:exists': 'file_dialog.fileExists'
  }
}
```

### 3. 启动应用

```bash
# 开发模式
npm run dev

# 或者如果有 Electron 启动脚本
npm run electron:dev
```

## 使用方法

### 1. 基本使用

```javascript
import ffmpegHelper from '@/utils/ffmpegHelper'
import electronFileHandler from '@/utils/electronFileHandler'

// 初始化 FFmpeg
const result = await ffmpegHelper.initialize()
if (result.success) {
  console.log('FFmpeg 初始化成功')
} else {
  console.error('初始化失败:', result.error)
}

// 选择文件
const files = await electronFileHandler.selectFiles({
  multiple: true,
  filters: [
    { name: '图片文件', extensions: ['jpg', 'png', 'gif'] }
  ]
})

// 生成视频
const videoResult = await ffmpegHelper.generateTransitionVideo(
  files[0], files[1], {
    duration: 5,
    effect: 'fade',
    width: 1280,
    height: 720
  }
)
```

### 2. Vue 组件集成

```vue
<template>
  <div>
    <ElectronFFmpegTest />
  </div>
</template>

<script>
import ElectronFFmpegTest from '@/components/ElectronFFmpegTest.vue'

export default {
  components: {
    ElectronFFmpegTest
  }
}
</script>
```

## 故障排除

### 1. SharedArrayBuffer 不可用

**症状**：控制台显示 "SharedArrayBuffer不可用" 错误

**解决方案**：
1. 确保 FFmpeg 安全服务已正确初始化
2. 检查命令行参数是否正确设置
3. 验证响应头配置

**调试命令**：
```javascript
console.log('SharedArrayBuffer 可用:', typeof SharedArrayBuffer !== 'undefined')
console.log('环境信息:', window.electronEnvironment)
```

### 2. FFmpeg 初始化失败

**症状**：FFmpeg 加载超时或网络错误

**解决方案**：
1. 检查网络连接
2. 尝试不同的 CDN 源
3. 查看控制台的详细错误信息

### 3. 文件选择不工作

**症状**：点击文件选择按钮无响应

**解决方案**：
1. 确保 IPC 路由配置正确
2. 检查文件对话框控制器是否正确注册
3. 验证预加载脚本是否正确加载

## 性能优化

### 1. 内存管理

```javascript
// 及时清理资源
await ffmpegHelper.cleanup()

// 释放 URL 对象
electronFileHandler.revokePreviewUrl(url)
```

### 2. 错误恢复

```javascript
// 实现重试机制
let retryCount = 0
const maxRetries = 3

while (retryCount < maxRetries) {
  try {
    await ffmpegHelper.initialize()
    break
  } catch (error) {
    retryCount++
    if (retryCount >= maxRetries) throw error
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
}
```

## 版本兼容性

- **ElectronEgg**: 2.x+
- **Electron**: 10.0+（推荐 20.0+）
- **Vue**: 3.x
- **FFmpeg.js**: 0.12.6
- **Node.js**: 14.0+

## 常见问题

**Q: 为什么在 Electron 中 SharedArrayBuffer 不可用？**
A: Electron 默认的安全策略限制了 SharedArrayBuffer。通过本指南的配置可以解决这个问题。

**Q: 视频生成速度很慢怎么办？**
A: 这是 WebAssembly 版本的 FFmpeg 的正常表现。可以考虑降低视频分辨率或使用更简单的效果。

**Q: 能否在生产环境中使用？**
A: 可以，但建议进行充分测试，特别是在不同的操作系统和 Electron 版本上。

## 技术支持

如果遇到问题，请提供以下信息：
- Electron 版本
- 操作系统信息
- 错误消息和堆栈跟踪
- 浏览器控制台日志

## 更新日志

- **v1.0.0**: 初始版本，支持基本的 FFmpeg.js 集成
- **v1.1.0**: 添加文件处理工具和测试组件
- **v1.2.0**: 增强错误处理和兼容性检查
