/**
 * 文件对话框控制器
 * 处理文件选择和保存对话框
 */

const { dialog } = require('electron')
const fs = require('fs').promises
const path = require('path')
const { logger } = require('ee-core/log')

class FileDialogController {
  
  /**
   * 显示打开文件对话框
   */
  async showOpenDialog(args) {
    const { options = {} } = args
    
    try {
      logger.info('[FileDialogController] 显示打开文件对话框:', options)
      
      const result = await dialog.showOpenDialog({
        properties: ['openFile'],
        filters: [
          { name: '图片文件', extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'] },
          { name: '所有文件', extensions: ['*'] }
        ],
        ...options
      })
      
      logger.info('[FileDialogController] 文件选择结果:', result)
      return result
      
    } catch (error) {
      logger.error('[FileDialogController] 打开文件对话框失败:', error)
      throw error
    }
  }

  /**
   * 显示保存文件对话框
   */
  async showSaveDialog(args) {
    const { options = {} } = args
    
    try {
      logger.info('[FileDialogController] 显示保存文件对话框:', options)
      
      const result = await dialog.showSaveDialog({
        filters: [
          { name: '视频文件', extensions: ['mp4'] },
          { name: '所有文件', extensions: ['*'] }
        ],
        ...options
      })
      
      logger.info('[FileDialogController] 文件保存结果:', result)
      return result
      
    } catch (error) {
      logger.error('[FileDialogController] 保存文件对话框失败:', error)
      throw error
    }
  }

  /**
   * 读取文件
   */
  async readFile(args) {
    const { filePath } = args
    
    try {
      // 安全检查：确保文件路径是绝对路径
      if (!path.isAbsolute(filePath)) {
        throw new Error('只允许访问绝对路径的文件')
      }
      
      logger.info('[FileDialogController] 读取文件:', filePath)
      
      const data = await fs.readFile(filePath)
      return data
      
    } catch (error) {
      logger.error('[FileDialogController] 读取文件失败:', filePath, error)
      throw error
    }
  }

  /**
   * 写入文件
   */
  async writeFile(args) {
    const { filePath, data } = args
    
    try {
      // 安全检查：确保文件路径是绝对路径
      if (!path.isAbsolute(filePath)) {
        throw new Error('只允许写入绝对路径的文件')
      }
      
      logger.info('[FileDialogController] 写入文件:', filePath)
      
      // 确保目录存在
      const dir = path.dirname(filePath)
      await fs.mkdir(dir, { recursive: true })
      
      await fs.writeFile(filePath, data)
      return true
      
    } catch (error) {
      logger.error('[FileDialogController] 写入文件失败:', filePath, error)
      throw error
    }
  }

  /**
   * 获取文件信息
   */
  async getFileInfo(args) {
    const { filePath } = args
    
    try {
      // 安全检查：确保文件路径是绝对路径
      if (!path.isAbsolute(filePath)) {
        throw new Error('只允许访问绝对路径的文件')
      }
      
      logger.info('[FileDialogController] 获取文件信息:', filePath)
      
      const stats = await fs.stat(filePath)
      return {
        size: stats.size,
        mtime: stats.mtime.getTime(),
        ctime: stats.ctime.getTime(),
        isFile: stats.isFile(),
        isDirectory: stats.isDirectory()
      }
      
    } catch (error) {
      logger.error('[FileDialogController] 获取文件信息失败:', filePath, error)
      throw error
    }
  }

  /**
   * 检查文件是否存在
   */
  async fileExists(args) {
    const { filePath } = args

    try {
      // 安全检查：确保文件路径是绝对路径
      if (!path.isAbsolute(filePath)) {
        return false
      }

      await fs.access(filePath)
      return true

    } catch (error) {
      return false
    }
  }

  /**
   * 获取应用资源文件路径
   */
  async getAssetPath(args) {
    const { fileName } = args

    try {
      logger.info('[FileDialogController] 获取资源文件路径:', fileName)

      // 获取应用资源目录
      const { app } = require('electron')
      const isDevelopment = process.env.NODE_ENV === 'development'

      let assetPath
      if (isDevelopment) {
        // 开发环境：从源码目录获取
        assetPath = path.join(__dirname, '../../frontend/src/assets', fileName)
      } else {
        // 生产环境：从打包后的资源目录获取
        assetPath = path.join(process.resourcesPath, 'app/frontend/src/assets', fileName)
      }

      // 检查文件是否存在
      try {
        await fs.access(assetPath)
        logger.info('[FileDialogController] 资源文件路径:', assetPath)
        return assetPath
      } catch (error) {
        // 如果文件不存在，尝试其他可能的路径
        const alternativePaths = [
          path.join(app.getAppPath(), 'frontend/src/assets', fileName),
          path.join(app.getAppPath(), 'src/assets', fileName),
          path.join(app.getAppPath(), 'assets', fileName)
        ]

        for (const altPath of alternativePaths) {
          try {
            await fs.access(altPath)
            logger.info('[FileDialogController] 找到替代资源文件路径:', altPath)
            return altPath
          } catch (e) {
            continue
          }
        }

        throw new Error(`资源文件不存在: ${fileName}`)
      }

    } catch (error) {
      logger.error('[FileDialogController] 获取资源文件路径失败:', fileName, error)
      throw error
    }
  }
}

module.exports = FileDialogController
