/**
 * IPC 路由配置示例
 * 用于配置 FFmpeg 相关的 IPC 通信路由
 * 
 * 将此配置添加到您的 ElectronEgg 配置文件中
 */

module.exports = {
  // FFmpeg 文件对话框相关路由
  'dialog:showOpenDialog': 'file_dialog.showOpenDialog',
  'dialog:showSaveDialog': 'file_dialog.showSaveDialog',

  // 文件系统操作路由
  'fs:readFile': 'file_dialog.readFile',
  'fs:writeFile': 'file_dialog.writeFile',
  'fs:getFileInfo': 'file_dialog.getFileInfo',
  'fs:exists': 'file_dialog.fileExists',

  // 应用资源访问路由
  'app:getAssetPath': 'file_dialog.getAssetPath',

  // FFmpeg 相关路由（如果需要主进程处理）
  'ffmpeg:initialize': 'ffmpeg.initialize',
  'ffmpeg:process': 'ffmpeg.processVideo',
  'ffmpeg:cleanup': 'ffmpeg.cleanup'
}

/**
 * 完整的配置文件示例 (config.default.js)
 */
const completeConfig = {
  // 应用基本配置
  appInfo: {
    name: 'baby-color-doppler',
    version: '1.0.0'
  },

  // 窗口配置
  windowsOption: {
    width: 1200,
    height: 800,
    show: false, // 延迟显示，避免白屏
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: false, // 开发环境中禁用，生产环境建议启用
      allowRunningInsecureContent: true,
      experimentalFeatures: true
    }
  },

  // IPC 路由配置
  ipc: {
    // 文件对话框
    'dialog:showOpenDialog': 'file_dialog.showOpenDialog',
    'dialog:showSaveDialog': 'file_dialog.showSaveDialog',
    
    // 文件系统
    'fs:readFile': 'file_dialog.readFile',
    'fs:writeFile': 'file_dialog.writeFile',
    'fs:getFileInfo': 'file_dialog.getFileInfo',
    'fs:exists': 'file_dialog.fileExists',
    
    // 其他现有路由...
    'controller.example.hello': 'example.hello',
    'controller.example.ping': 'example.ping'
  },

  // 开发环境配置
  development: {
    hostname: 'localhost',
    port: 7070,
    protocol: 'http'
  },

  // 生产环境配置
  production: {
    hostname: 'localhost',
    port: 7071,
    protocol: 'http'
  }
}

// 如果您使用的是标准的 ElectronEgg 配置，请将上述 IPC 路由添加到您的配置文件中
