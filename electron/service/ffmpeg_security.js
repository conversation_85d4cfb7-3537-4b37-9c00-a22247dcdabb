/**
 * FFmpeg 安全配置服务
 * 用于配置 Electron 环境以支持 FFmpeg.js
 */

const { session, app } = require('electron')
const { logger } = require('ee-core/log')

class FFmpegSecurityService {
  constructor() {
    this.initialized = false
  }

  /**
   * 创建并初始化 FFmpeg 安全配置
   */
  create() {
    if (this.initialized) {
      return
    }

    logger.info('[FFmpegSecurityService] 初始化 FFmpeg 安全配置...')

    try {
      this.configureCommandLineArgs()
      this.configureSessionSecurity()
      this.setupResponseHeaders()
      this.setupRequestHeaders()

      this.initialized = true
      logger.info('[FFmpegSecurityService] FFmpeg 安全配置初始化完成')
    } catch (error) {
      logger.error('[FFmpegSecurityService] 初始化失败:', error)
    }
  }

  /**
   * 配置命令行参数以支持 SharedArrayBuffer
   */
  configureCommandLineArgs() {
    logger.info('[FFmpegSecurityService] 配置命令行参数...')

    // 启用 SharedArrayBuffer 相关功能
    app.commandLine.appendSwitch('enable-features', 'SharedArrayBuffer')
    app.commandLine.appendSwitch('enable-blink-features', 'SharedArrayBuffer')
    app.commandLine.appendSwitch('cross-origin-isolated')

    // 禁用某些安全限制（开发环境）
    if (process.env.NODE_ENV === 'development') {
      app.commandLine.appendSwitch('disable-web-security')
      app.commandLine.appendSwitch('allow-running-insecure-content')
      app.commandLine.appendSwitch('disable-features', 'VizDisplayCompositor')
    }

    // 启用实验性功能
    app.commandLine.appendSwitch('enable-experimental-web-platform-features')

    logger.info('[FFmpegSecurityService] 命令行参数配置完成')
  }

  /**
   * 配置会话安全设置
   */
  configureSessionSecurity() {
    logger.info('[FFmpegSecurityService] 配置会话安全设置...')

    const defaultSession = session.defaultSession

    // 设置权限策略
    defaultSession.setPermissionRequestHandler((webContents, permission, callback) => {
      // 允许所有权限请求（开发环境）
      if (process.env.NODE_ENV === 'development') {
        callback(true)
      } else {
        // 生产环境中更严格的权限控制
        const allowedPermissions = ['notifications', 'media']
        callback(allowedPermissions.includes(permission))
      }
    })

    logger.info('[FFmpegSecurityService] 会话安全设置配置完成')
  }

  /**
   * 设置响应头以支持 SharedArrayBuffer
   */
  setupResponseHeaders() {
    logger.info('[FFmpegSecurityService] 设置响应头...')

    const defaultSession = session.defaultSession

    defaultSession.webRequest.onHeadersReceived((details, callback) => {
      const responseHeaders = details.responseHeaders || {}

      // 设置必要的安全头以支持 SharedArrayBuffer
      responseHeaders['Cross-Origin-Opener-Policy'] = ['same-origin']
      responseHeaders['Cross-Origin-Embedder-Policy'] = ['require-corp']
      responseHeaders['Cross-Origin-Resource-Policy'] = ['cross-origin']

      // 设置权限策略
      responseHeaders['Permissions-Policy'] = [
        'cross-origin-isolated=*',
        'shared-array-buffer=*'
      ]

      // default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https:;
      // 设置内容安全策略以允许 WebAssembly 和 Worker
      responseHeaders['Content-Security-Policy'] = [
        "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: http: blob: https:; " +
        "script-src 'self' 'unsafe-inline' 'unsafe-eval' http: https: blob:; " +
        "worker-src 'self' blob: data:; " +
        "connect-src 'self' http: https: wss: blob:; " +
        "img-src 'self' data: http: blob: https:; " +
        "media-src 'self' blob: data:; " +
        "object-src 'none'; " +
        "base-uri 'self';"
      ]

      // 开发环境中设置更宽松的 CORS 策略
      if (process.env.NODE_ENV === 'development') {
        responseHeaders['Access-Control-Allow-Origin'] = ['*']
        responseHeaders['Access-Control-Allow-Methods'] = ['GET, POST, PUT, DELETE, OPTIONS']
        responseHeaders['Access-Control-Allow-Headers'] = ['*']
        responseHeaders['Access-Control-Allow-Credentials'] = ['true']
      }

      callback({ responseHeaders })
    })

    logger.info('[FFmpegSecurityService] 响应头设置完成')
  }

  /**
   * 设置请求头以支持 CORS
   */
  setupRequestHeaders() {
    logger.info('[FFmpegSecurityService] 设置请求头...')

    const defaultSession = session.defaultSession

    defaultSession.webRequest.onBeforeSendHeaders((details, callback) => {
      const requestHeaders = details.requestHeaders || {}

      // 为 FFmpeg 核心文件请求添加必要的头
      if (details.url.includes('ffmpeg-core') ||
          details.url.includes('@ffmpeg') ||
          details.url.includes('unpkg.com') ||
          details.url.includes('jsdelivr.net')) {

        requestHeaders['Access-Control-Request-Method'] = 'GET'
        requestHeaders['Access-Control-Request-Headers'] = 'content-type'
        requestHeaders['Origin'] = 'electron://app'
      }

      callback({ requestHeaders })
    })

    logger.info('[FFmpegSecurityService] 请求头设置完成')
  }

  /**
   * 获取安全的窗口选项
   */
  getSecureWindowOptions(isDevelopment = false) {
    return {
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        webSecurity: !isDevelopment, // 开发环境中禁用 web 安全性
        allowRunningInsecureContent: isDevelopment,
        experimentalFeatures: true,
        // 尝试启用 SharedArrayBuffer 相关功能
        additionalArguments: [
          '--enable-features=SharedArrayBuffer',
          '--enable-blink-features=SharedArrayBuffer',
          '--cross-origin-isolated'
        ]
      }
    }
  }

  /**
   * 检查 SharedArrayBuffer 支持
   */
  checkSharedArrayBufferSupport() {
    try {
      // 尝试创建 SharedArrayBuffer
      const sab = new SharedArrayBuffer(1024)
      logger.info('[FFmpegSecurityService] SharedArrayBuffer 支持检查: 通过')
      return true
    } catch (error) {
      logger.warn('[FFmpegSecurityService] SharedArrayBuffer 支持检查: 失败', error)
      return false
    }
  }

  /**
   * 设置环境变量
   */
  setupEnvironmentVariables() {
    // 设置环境变量以支持 FFmpeg
    process.env.FFMPEG_ELECTRON_MODE = 'true'
    process.env.CROSS_ORIGIN_ISOLATED = 'true'
    process.env.SHARED_ARRAY_BUFFER_ENABLED = 'true'

    logger.info('[FFmpegSecurityService] 环境变量设置完成')
  }

  /**
   * 销毁服务
   */
  destroy() {
    if (this.initialized) {
      logger.info('[FFmpegSecurityService] 销毁 FFmpeg 安全配置服务')
      this.initialized = false
    }
  }
}

/**
 * 创建服务实例
 */
const ffmpegSecurityService = new FFmpegSecurityService()

module.exports = {
  ffmpegSecurityService
}
