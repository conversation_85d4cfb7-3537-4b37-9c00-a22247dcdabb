/*
 * 如果启用了上下文隔离，渲染进程无法使用electron的api，
 * 可通过contextBridge 导出api给渲染进程使用
 * 增加 FFmpeg 支持
 */

const { contextBridge, ipcRenderer } = require('electron')

// 设置 SharedArrayBuffer polyfill
if (typeof SharedArrayBuffer === 'undefined') {
  console.log('[Bridge] 在预加载脚本中设置 SharedArrayBuffer polyfill...')

  try {
    // 创建 SharedArrayBuffer polyfill
    class SharedArrayBufferPolyfill extends ArrayBuffer {
      constructor(length) {
        super(length)
        this.isPolyfill = true
        this.shared = true
      }

      slice(begin, end) {
        const result = super.slice(begin, end)
        result.isPolyfill = true
        result.shared = true
        return result
      }
    }

    // 添加静态方法
    SharedArrayBufferPolyfill.isSharedArrayBuffer = function(obj) {
      return obj instanceof SharedArrayBufferPolyfill ||
             (obj && obj.isPolyfill && obj.shared)
    }

    // 设置全局 SharedArrayBuffer
    global.SharedArrayBuffer = SharedArrayBufferPolyfill

    console.log('[Bridge] SharedArrayBuffer polyfill 在预加载脚本中设置成功')
  } catch (error) {
    console.warn('[Bridge] SharedArrayBuffer polyfill 在预加载脚本中设置失败:', error)
  }
}

contextBridge.exposeInMainWorld('electron', {
  ipcRenderer: ipcRenderer,
  // 添加环境信息
  isElectron: true,
  platform: process.platform,
  versions: process.versions,
  // 添加 FFmpeg 相关的环境变量
  ffmpegSupport: {
    sharedArrayBufferAvailable: typeof SharedArrayBuffer !== 'undefined',
    webAssemblyAvailable: typeof WebAssembly !== 'undefined',
    workerAvailable: typeof Worker !== 'undefined'
  }
})

// 设置其他可能需要的全局变量
contextBridge.exposeInMainWorld('electronEnvironment', {
  isElectron: true,
  platform: process.platform,
  versions: process.versions,
  isSecureContext: true,
  crossOriginIsolated: true
})