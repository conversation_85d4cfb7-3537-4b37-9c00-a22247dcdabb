/*************************************************
 ** preload为预加载模块，该文件将会在程序启动时加载 **
 *************************************************/

const { logger } = require('ee-core/log');
const { trayService } = require('../service/os/tray');
const { securityService } = require('../service/os/security');
const { autoUpdaterService } = require('../service/os/auto_updater');
const { crossService } = require('../service/cross');
const { sqlitedbService } = require('../service/database/sqlitedb');
const { ffmpegSecurityService } = require('../service/ffmpeg_security');

function preload() {
  // 示例功能模块，可选择性使用和修改
  logger.info('[preload] load 6 - 包含 FFmpeg 支持');

  // 首先初始化 FFmpeg 安全配置
  ffmpegSecurityService.create();

  trayService.create();
  securityService.create();
  autoUpdaterService.create();

  // go server
  //crossService.createGoServer();
  crossService.createJavaServer();
  // init sqlite db
  sqlitedbService.init();
}

/**
* 预加载模块入口
*/
module.exports = {
  preload
}
