/**
 * 验证 Element Plus Icons Vue 导入的脚本
 * 检查是否有不存在的图标导入
 */

const fs = require('fs')
const path = require('path')

// Element Plus Icons Vue 中实际存在的图标列表（部分常用的）
const validIcons = [
  'Plus', 'Edit', 'Delete', 'QuestionFilled', 'VideoPlay', 'Download', 'Share',
  'View', 'Right', 'VideoPause', 'Refresh', 'Check', 'Loading', 'Upload',
  'Headset', 'Microphone', 'Mute', 'Mic', 'Notification', 'Close', 'Search',
  'Setting', 'User', 'Lock', 'Unlock', 'Star', 'StarFilled', 'Heart',
  'HeartFilled', 'Warning', 'WarningFilled', 'Info', 'InfoFilled',
  'Success', 'SuccessFilled', 'Error', 'ErrorFilled', 'CircleCheck',
  'CircleClose', 'CirclePlus', 'Remove', 'ZoomIn', 'ZoomOut', 'Back',
  'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'More', 'MoreFilled'
]

// 不存在的图标（常见错误）
const invalidIcons = ['Audio', 'Sound', 'Music', 'Speaker', 'Headphone']

function checkFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    const errors = []
    
    // 检查导入语句
    const importRegex = /import\s*{([^}]+)}\s*from\s*['"]@element-plus\/icons-vue['"]/g
    const importMatches = content.matchAll(importRegex)
    
    for (const match of importMatches) {
      const importedIcons = match[1]
        .split(',')
        .map(icon => icon.trim())
        .filter(icon => icon.length > 0)
      
      for (const icon of importedIcons) {
        if (invalidIcons.includes(icon)) {
          errors.push({
            type: 'invalid_import',
            icon,
            line: getLineNumber(content, match.index),
            suggestion: getSuggestion(icon)
          })
        }
      }
    }
    
    // 检查模板中的图标使用
    const templateRegex = /<el-icon[^>]*><(\w+)\s*\/><\/el-icon>/g
    const templateMatches = content.matchAll(templateRegex)
    
    for (const match of templateMatches) {
      const iconName = match[1]
      if (invalidIcons.includes(iconName)) {
        errors.push({
          type: 'invalid_usage',
          icon: iconName,
          line: getLineNumber(content, match.index),
          suggestion: getSuggestion(iconName)
        })
      }
    }
    
    return errors
  } catch (error) {
    console.error(`读取文件失败: ${filePath}`, error)
    return []
  }
}

function getLineNumber(content, index) {
  return content.substring(0, index).split('\n').length
}

function getSuggestion(invalidIcon) {
  const suggestions = {
    'Audio': 'Headset',
    'Sound': 'Headset',
    'Music': 'Headset',
    'Speaker': 'Headset',
    'Headphone': 'Headset'
  }
  return suggestions[invalidIcon] || 'Unknown'
}

function scanDirectory(dir) {
  const results = []
  
  function scan(currentDir) {
    const files = fs.readdirSync(currentDir)
    
    for (const file of files) {
      const fullPath = path.join(currentDir, file)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
        scan(fullPath)
      } else if (file.endsWith('.vue') || file.endsWith('.js') || file.endsWith('.ts')) {
        const errors = checkFile(fullPath)
        if (errors.length > 0) {
          results.push({
            file: fullPath,
            errors
          })
        }
      }
    }
  }
  
  scan(dir)
  return results
}

// 主函数
function main() {
  console.log('🔍 检查 Element Plus Icons Vue 导入...\n')
  
  const srcDir = path.join(__dirname, 'src')
  const results = scanDirectory(srcDir)
  
  if (results.length === 0) {
    console.log('✅ 所有图标导入都是正确的！')
    return
  }
  
  console.log('❌ 发现以下问题：\n')
  
  for (const result of results) {
    console.log(`📁 文件: ${result.file}`)
    
    for (const error of result.errors) {
      console.log(`   ❌ 第 ${error.line} 行: ${error.type === 'invalid_import' ? '导入' : '使用'} 了不存在的图标 "${error.icon}"`)
      console.log(`   💡 建议使用: "${error.suggestion}"`)
    }
    
    console.log('')
  }
  
  console.log('🔧 请修复上述问题后重新运行验证。')
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}

module.exports = {
  checkFile,
  scanDirectory,
  validIcons,
  invalidIcons
}
