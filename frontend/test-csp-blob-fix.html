<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSP Blob URL 修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 1000px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .success { color: #67c23a; font-weight: bold; }
        .error { color: #f56c6c; font-weight: bold; }
        .warning { color: #e6a23c; font-weight: bold; }
        .info { color: #409eff; font-weight: bold; }
        .test-image {
            max-width: 200px;
            max-height: 150px;
            border: 2px solid #ddd;
            border-radius: 5px;
            margin: 10px;
        }
        .test-video {
            max-width: 300px;
            max-height: 200px;
            border: 2px solid #ddd;
            border-radius: 5px;
            margin: 10px;
        }
        button {
            background: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #66b1ff; }
        .highlight {
            background: #e8f4fd;
            padding: 15px;
            border-left: 4px solid #409eff;
            margin: 15px 0;
        }
        .code {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔧 CSP Blob URL 修复测试</h1>
    
    <div class="highlight">
        <h3>🎯 修复的 CSP 问题</h3>
        <p><strong>原始错误</strong>: "Refused to load the image 'blob:http://localhost:8080/...' because it violates the following Content Security Policy directive: "img-src 'self' data:""</p>
        <p><strong>根本原因</strong>: CSP 配置中缺少 blob: 支持</p>
        <p><strong>修复策略</strong>: 在多个层级添加 blob: 支持</p>
    </div>

    <div class="test-section">
        <h2>🛠️ 修复内容总结</h2>
        
        <h3>1. HTML Meta 标签 CSP 修复</h3>
        <div class="code">修复前: img-src 'self' data:
修复后: img-src 'self' data: blob:</div>
        
        <h3>2. Vite 开发服务器 CSP 修复</h3>
        <div class="code">添加了: 'Content-Security-Policy': "...img-src 'self' data: blob:; media-src 'self' blob: data:..."</div>
        
        <h3>3. Electron 安全服务修复</h3>
        <div class="code">确保 ffmpegSecurityService.create() 被调用
CSP 包含: "img-src 'self' data: http: blob: https:;"</div>
        
        <h3>4. 新增的 CSP 指令</h3>
        <ul>
            <li>✅ <strong>img-src</strong>: 添加了 blob: 支持</li>
            <li>✅ <strong>media-src</strong>: 添加了 blob: 和 data: 支持</li>
            <li>✅ <strong>worker-src</strong>: 添加了 blob: 支持（FFmpeg.js 需要）</li>
            <li>✅ <strong>object-src</strong>: 设置为 'none' 提高安全性</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🧪 Blob URL 测试</h2>
        
        <button onclick="testImageBlob()">测试图片 Blob URL</button>
        <button onclick="testVideoBlob()">测试视频 Blob URL</button>
        <button onclick="testCanvasBlob()">测试 Canvas Blob URL</button>
        <button onclick="checkCSP()">检查当前 CSP</button>
        <button onclick="clearTests()">清除测试</button>
        
        <div id="testResults" style="margin-top: 20px;">
            <div class="info">点击按钮开始测试...</div>
        </div>
        
        <div id="testImages" style="margin-top: 20px;">
            <!-- 测试图片将显示在这里 -->
        </div>
    </div>

    <div class="test-section">
        <h2>📊 CSP 验证</h2>
        
        <h3>当前页面的 CSP 策略:</h3>
        <div id="currentCSP" class="code">检查中...</div>
        
        <h3>预期的 CSP 指令:</h3>
        <div class="code">img-src 'self' data: blob:
media-src 'self' blob: data:
worker-src 'self' blob:
object-src 'none'</div>
    </div>

    <div class="test-section">
        <h2>🔍 故障排除指南</h2>
        
        <h3>如果 Blob URL 仍然被阻止:</h3>
        <ol>
            <li><strong>清除浏览器缓存</strong>: 确保加载最新的 CSP 配置</li>
            <li><strong>重启 Electron 应用</strong>: 确保新的安全配置生效</li>
            <li><strong>检查开发者工具</strong>: 查看 Network 标签中的响应头</li>
            <li><strong>验证 CSP 头部</strong>: 确认 Content-Security-Policy 包含 blob:</li>
        </ol>
        
        <h3>成功指标:</h3>
        <ul class="success">
            <li>✅ 图片 Blob URL 正常显示</li>
            <li>✅ 视频 Blob URL 正常播放</li>
            <li>✅ 控制台没有 CSP 违规错误</li>
            <li>✅ FFmpeg.js 生成的内容可见</li>
        </ul>
    </div>

    <script>
        // 检查当前 CSP
        function checkCurrentCSP() {
            const metaCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]')
            if (metaCSP) {
                document.getElementById('currentCSP').textContent = metaCSP.content
            } else {
                document.getElementById('currentCSP').textContent = '未找到 Meta CSP 标签，可能通过 HTTP 头部设置'
            }
        }

        function testImageBlob() {
            const resultsDiv = document.getElementById('testResults')
            const imagesDiv = document.getElementById('testImages')
            
            resultsDiv.innerHTML = '<div class="info">创建图片 Blob URL 测试...</div>'
            
            try {
                // 创建一个简单的图片
                const canvas = document.createElement('canvas')
                canvas.width = 200
                canvas.height = 150
                const ctx = canvas.getContext('2d')
                
                // 绘制测试图片
                ctx.fillStyle = '#4CAF50'
                ctx.fillRect(0, 0, 200, 150)
                ctx.fillStyle = '#FFFFFF'
                ctx.font = '20px Arial'
                ctx.fillText('Blob Test', 50, 80)
                
                // 转换为 Blob
                canvas.toBlob((blob) => {
                    const blobUrl = URL.createObjectURL(blob)
                    console.log('创建的 Blob URL:', blobUrl)
                    
                    // 创建图片元素
                    const img = document.createElement('img')
                    img.src = blobUrl
                    img.className = 'test-image'
                    img.alt = 'Blob URL 测试图片'
                    
                    img.onload = () => {
                        resultsDiv.innerHTML = `
                            <div class="success">✅ 图片 Blob URL 测试成功</div>
                            <div class="info">Blob URL: ${blobUrl}</div>
                        `
                    }
                    
                    img.onerror = (error) => {
                        resultsDiv.innerHTML = `
                            <div class="error">❌ 图片 Blob URL 测试失败</div>
                            <div class="error">可能仍有 CSP 限制</div>
                        `
                        console.error('图片加载失败:', error)
                    }
                    
                    imagesDiv.appendChild(img)
                })
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">❌ 创建 Blob URL 失败</div>
                    <div class="error">错误: ${error.message}</div>
                `
            }
        }

        function testVideoBlob() {
            const resultsDiv = document.getElementById('testResults')
            const imagesDiv = document.getElementById('testImages')
            
            resultsDiv.innerHTML = '<div class="info">模拟视频 Blob URL 测试...</div>'
            
            // 模拟 FFmpeg.js 生成的视频数据
            const mockVideoData = new Uint8Array([
                // 这里是模拟的视频数据头部
                0x00, 0x00, 0x00, 0x20, 0x66, 0x74, 0x79, 0x70
            ])
            
            try {
                const blob = new Blob([mockVideoData], { type: 'video/mp4' })
                const blobUrl = URL.createObjectURL(blob)
                console.log('创建的视频 Blob URL:', blobUrl)
                
                // 创建视频元素
                const video = document.createElement('video')
                video.src = blobUrl
                video.className = 'test-video'
                video.controls = true
                video.muted = true
                
                video.onloadstart = () => {
                    resultsDiv.innerHTML = `
                        <div class="success">✅ 视频 Blob URL 创建成功</div>
                        <div class="info">Blob URL: ${blobUrl}</div>
                        <div class="warning">注意: 这是模拟数据，可能无法播放</div>
                    `
                }
                
                video.onerror = (error) => {
                    resultsDiv.innerHTML = `
                        <div class="warning">⚠️ 视频 Blob URL 创建成功，但播放失败</div>
                        <div class="info">这是正常的，因为使用了模拟数据</div>
                        <div class="success">重要: 没有 CSP 错误说明 blob: 支持正常</div>
                    `
                }
                
                imagesDiv.appendChild(video)
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">❌ 创建视频 Blob URL 失败</div>
                    <div class="error">错误: ${error.message}</div>
                `
            }
        }

        function testCanvasBlob() {
            const resultsDiv = document.getElementById('testResults')
            const imagesDiv = document.getElementById('testImages')
            
            resultsDiv.innerHTML = '<div class="info">测试 Canvas 生成的 Blob URL...</div>'
            
            try {
                // 创建一个动态 Canvas
                const canvas = document.createElement('canvas')
                canvas.width = 300
                canvas.height = 200
                const ctx = canvas.getContext('2d')
                
                // 绘制渐变背景
                const gradient = ctx.createLinearGradient(0, 0, 300, 200)
                gradient.addColorStop(0, '#FF6B6B')
                gradient.addColorStop(1, '#4ECDC4')
                ctx.fillStyle = gradient
                ctx.fillRect(0, 0, 300, 200)
                
                // 添加文字
                ctx.fillStyle = '#FFFFFF'
                ctx.font = 'bold 24px Arial'
                ctx.textAlign = 'center'
                ctx.fillText('Canvas Blob', 150, 100)
                ctx.fillText('URL Test', 150, 130)
                
                // 转换为 Blob URL
                canvas.toBlob((blob) => {
                    const blobUrl = URL.createObjectURL(blob)
                    
                    const img = document.createElement('img')
                    img.src = blobUrl
                    img.className = 'test-image'
                    img.alt = 'Canvas Blob URL 测试'
                    
                    img.onload = () => {
                        resultsDiv.innerHTML = `
                            <div class="success">✅ Canvas Blob URL 测试成功</div>
                            <div class="info">这模拟了 FFmpeg.js 生成图片的场景</div>
                        `
                    }
                    
                    img.onerror = () => {
                        resultsDiv.innerHTML = `
                            <div class="error">❌ Canvas Blob URL 测试失败</div>
                            <div class="error">CSP 可能仍然阻止 blob: URLs</div>
                        `
                    }
                    
                    imagesDiv.appendChild(img)
                }, 'image/png')
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">❌ Canvas Blob 测试失败</div>
                    <div class="error">错误: ${error.message}</div>
                `
            }
        }

        function checkCSP() {
            const resultsDiv = document.getElementById('testResults')
            
            // 检查 CSP 违规
            let cspViolations = []
            
            // 监听 CSP 违规事件
            document.addEventListener('securitypolicyviolation', (e) => {
                cspViolations.push({
                    directive: e.violatedDirective,
                    blockedURI: e.blockedURI,
                    originalPolicy: e.originalPolicy
                })
            })
            
            resultsDiv.innerHTML = `
                <div class="info">CSP 检查完成</div>
                <div class="info">当前违规数量: ${cspViolations.length}</div>
                <div class="success">如果没有违规，说明 CSP 配置正确</div>
            `
            
            checkCurrentCSP()
        }

        function clearTests() {
            document.getElementById('testResults').innerHTML = '<div class="info">测试已清除</div>'
            document.getElementById('testImages').innerHTML = ''
        }

        // 页面加载时检查 CSP
        window.onload = function() {
            console.log('CSP Blob URL 修复测试页面已加载')
            checkCurrentCSP()
            
            // 监听 CSP 违规
            document.addEventListener('securitypolicyviolation', (e) => {
                console.error('CSP 违规:', e)
            })
        }
    </script>
</body>
</html>
