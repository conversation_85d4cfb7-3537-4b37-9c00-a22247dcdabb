/**
 * FFmpeg.js 工具类
 * 用于在浏览器端和 Electron 环境中处理视频生成和过渡效果
 */

import { FFmpeg } from '@ffmpeg/ffmpeg'
import { fetchFile, toBlobURL } from '@ffmpeg/util'

class FFmpegHelper {
  constructor() {
    this.ffmpeg = null
    this.isLoaded = false
    this.isLoading = false
    this.progressCallback = null
    this.isElectron = this.detectElectronEnvironment()
    this.sharedArrayBufferPolyfilled = false

    // 在构造函数中初始化 Electron 兼容性
    if (this.isElectron) {
      this.setupElectronCompatibility()
    }
  }

  /**
   * 检测是否在 Electron 环境中运行
   */
  detectElectronEnvironment() {
    // 检查多种 Electron 环境标识
    return !!(
      typeof window !== 'undefined' &&
      (
        window.electron ||
        window.electronAPI ||
        navigator.userAgent.toLowerCase().indexOf('electron') > -1 ||
        (typeof process !== 'undefined' && process.versions?.electron) ||
        (typeof require !== 'undefined' && typeof require('electron') !== 'undefined')
      )
    )
  }

  /**
   * 设置 Electron 环境兼容性
   */
  setupElectronCompatibility() {
    console.log('检测到 Electron 环境，设置兼容性配置...')

    // 尝试设置 SharedArrayBuffer polyfill
    this.setupSharedArrayBufferPolyfill()

    // 设置 Electron 特有的配置
    this.setupElectronSpecificConfig()
  }

  /**
   * 设置 SharedArrayBuffer polyfill
   */
  setupSharedArrayBufferPolyfill() {
    if (typeof SharedArrayBuffer === 'undefined') {
      console.log('SharedArrayBuffer 不可用，尝试设置 polyfill...')

      try {
        // 高级的 SharedArrayBuffer polyfill
        if (typeof ArrayBuffer !== 'undefined') {
          class SharedArrayBufferPolyfill extends ArrayBuffer {
            constructor(length) {
              super(length)
              this.isPolyfill = true
              this.shared = true
            }

            slice(begin, end) {
              const result = super.slice(begin, end)
              result.isPolyfill = true
              result.shared = true
              return result
            }
          }

          // 添加静态方法
          SharedArrayBufferPolyfill.isSharedArrayBuffer = function(obj) {
            return obj instanceof SharedArrayBufferPolyfill ||
                   (obj && obj.isPolyfill && obj.shared)
          }

          window.SharedArrayBuffer = SharedArrayBufferPolyfill
          this.sharedArrayBufferPolyfilled = true
          console.log('SharedArrayBuffer polyfill 设置成功')
        }
      } catch (error) {
        console.warn('SharedArrayBuffer polyfill 设置失败:', error)
      }
    }
  }

  /**
   * 设置 Electron 特有的配置
   */
  setupElectronSpecificConfig() {
    // 设置 Electron 环境下的特殊配置
    if (typeof window !== 'undefined') {
      // 确保 window.isSecureContext 为 true（Electron 环境下应该是安全的）
      if (typeof window.isSecureContext === 'undefined') {
        Object.defineProperty(window, 'isSecureContext', {
          value: true,
          writable: false,
          configurable: false
        })
      }

      // 设置 crossOriginIsolated 为 true（如果需要）
      if (typeof window.crossOriginIsolated === 'undefined') {
        Object.defineProperty(window, 'crossOriginIsolated', {
          value: true,
          writable: false,
          configurable: false
        })
      }

      // 设置 Electron 环境标识
      window.electronFFmpegMode = true
    }
  }

  /**
   * 初始化FFmpeg实例（支持 Electron 环境）
   */
  async initialize(progressCallback = null) {
    if (this.isLoaded) return { success: true }
    if (this.isLoading) {
      // 等待加载完成
      while (this.isLoading) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      return { success: this.isLoaded }
    }

    this.isLoading = true
    this.progressCallback = progressCallback

    try {
      console.log(`开始初始化 FFmpeg (${this.isElectron ? 'Electron' : '浏览器'} 环境)...`)

      // 检查浏览器兼容性
      const compatibilityCheck = this.checkBrowserCompatibility()

      // 在 Electron 环境中，即使有警告也尝试继续
      if (!compatibilityCheck.compatible) {
        if (this.isElectron && compatibilityCheck.errors.length === 0) {
          console.warn('Electron 环境兼容性检查有警告，但尝试继续初始化:', compatibilityCheck.warnings)
        } else {
          throw new Error(`兼容性检查失败: ${compatibilityCheck.errors.join(', ')}`)
        }
      }

      if (this.progressCallback) {
        this.progressCallback({
          type: 'status',
          message: `初始化FFmpeg实例 (${this.isElectron ? 'Electron模式' : '浏览器模式'})...`
        })
      }

      // 在 Electron 环境中进行额外的初始化步骤
      if (this.isElectron) {
        await this.initializeElectronSpecific()
      }

      // 测试基础功能
      if (this.progressCallback) {
        this.progressCallback({
          type: 'status',
          message: '测试 FFmpeg 基础功能...'
        })
      }

      try {
        await this.testBasicFunctionality()
        console.log('FFmpeg 基础功能测试通过')
      } catch (testError) {
        console.warn('FFmpeg 基础功能测试失败，但继续初始化:', testError)
      }

      this.ffmpeg = new FFmpeg()

      // 设置进度监听
      this.ffmpeg.on('progress', ({ progress, time }) => {
        if (this.progressCallback) {
          this.progressCallback({
            type: 'progress',
            progress: Math.round(progress * 100),
            time
          })
        }
      })

      // 设置日志监听
      this.ffmpeg.on('log', ({ message }) => {
        console.log('FFmpeg:', message)
        if (this.progressCallback) {
          this.progressCallback({
            type: 'log',
            message
          })
        }
      })

      if (this.progressCallback) {
        this.progressCallback({
          type: 'status',
          message: '正在加载FFmpeg核心文件...'
        })
      }

      // 根据环境选择不同的加载策略
      const loadSuccess = await this.loadFFmpegCore()

      if (!loadSuccess) {
        throw new Error('FFmpeg 核心文件加载失败')
      }

      this.isLoaded = true
      this.isLoading = false

      if (this.progressCallback) {
        this.progressCallback({
          type: 'status',
          message: 'FFmpeg加载完成'
        })
      }

      console.log('FFmpeg 初始化成功')
      return { success: true }

    } catch (error) {
      this.isLoading = false
      console.error('FFmpeg初始化失败:', error)

      // 提供更详细的错误信息
      const errorMessage = this.getDetailedErrorMessage(error)

      return { success: false, error: errorMessage }
    }
  }

  /**
   * Electron 环境特定的初始化
   */
  async initializeElectronSpecific() {
    console.log('执行 Electron 特定初始化...')

    // 尝试设置更多的兼容性 polyfill
    if (typeof SharedArrayBuffer === 'undefined' && !this.sharedArrayBufferPolyfilled) {
      console.log('尝试更高级的 SharedArrayBuffer polyfill...')
      this.setupAdvancedSharedArrayBufferPolyfill()
    }

    // 设置 Electron 特有的网络配置
    this.setupElectronNetworkConfig()
  }

  /**
   * 设置高级 SharedArrayBuffer polyfill
   */
  setupAdvancedSharedArrayBufferPolyfill() {
    try {
      // 更完整的 SharedArrayBuffer polyfill
      if (typeof ArrayBuffer !== 'undefined' && typeof SharedArrayBuffer === 'undefined') {
        class SharedArrayBufferPolyfill extends ArrayBuffer {
          constructor(length) {
            super(length)
            this.isPolyfill = true
            this.shared = true
          }

          slice(begin, end) {
            const result = super.slice(begin, end)
            result.isPolyfill = true
            result.shared = true
            return result
          }
        }

        // 添加静态方法
        SharedArrayBufferPolyfill.isSharedArrayBuffer = function(obj) {
          return obj instanceof SharedArrayBufferPolyfill ||
                 (obj && obj.isPolyfill && obj.shared)
        }

        window.SharedArrayBuffer = SharedArrayBufferPolyfill
        this.sharedArrayBufferPolyfilled = true

        console.log('高级 SharedArrayBuffer polyfill 设置成功')
      }
    } catch (error) {
      console.warn('高级 SharedArrayBuffer polyfill 设置失败:', error)
    }
  }

  /**
   * 设置 Electron 网络配置
   */
  setupElectronNetworkConfig() {
    // 在 Electron 中，可能需要特殊的网络配置
    if (typeof window !== 'undefined') {
      // 设置一些可能需要的全局变量
      window.electronFFmpegMode = true
    }
  }

  /**
   * 执行视频生成（简化版本）
   */
  async executeVideoGeneration(effect, duration, fps, width, height, hasAudio, audioVolume) {
    console.log(`开始执行视频生成: hasAudio=${hasAudio}`)

    try {
      // 检查输入文件
      await this.verifyInputFiles(hasAudio)

      if (hasAudio) {
        // 两步法：先生成视频，再添加音频
        await this.generateVideoWithAudioTwoStep(effect, duration, fps, width, height, audioVolume)
      } else {
        // 直接生成无音频视频
        await this.generateVideoWithoutAudio(effect, duration, fps, width, height)
      }

      console.log('视频生成执行完成')

    } catch (execError) {
      console.error('视频生成执行失败:', execError)
      throw new Error(`视频生成执行失败: ${execError.message || execError}`)
    }
  }

  /**
   * 验证输入文件
   */
  async verifyInputFiles(hasAudio) {
    try {
      const startData = await this.ffmpeg.readFile('start.jpg')
      const endData = await this.ffmpeg.readFile('end.jpg')
      console.log(`输入文件检查: start.jpg (${startData.length} bytes), end.jpg (${endData.length} bytes)`)

      if (hasAudio) {
        const audioData = await this.ffmpeg.readFile('audio_adjusted.mp3')
        console.log(`音频文件检查: audio_adjusted.mp3 (${audioData.length} bytes)`)
      }
    } catch (checkError) {
      console.error('输入文件检查失败:', checkError)
      throw new Error(`输入文件不存在或无法访问: ${checkError.message}`)
    }
  }

  /**
   * 验证输出文件
   */
  async verifyOutputFile() {
    try {
      console.log('开始验证输出文件: output.mp4')

      // 首先尝试列出所有文件来调试
      try {
        const files = await this.ffmpeg.listDir('/')
        const fileNames = files.map(file => {
          if (typeof file === 'string') {
            return file
          } else if (file && file.name) {
            return file.name
          } else {
            return JSON.stringify(file)
          }
        })
        console.log('FFmpeg 文件系统中的所有文件:', fileNames)
      } catch (listError) {
        console.warn('无法列出文件系统内容:', listError)
      }

      // 尝试读取输出文件
      const outputData = await this.ffmpeg.readFile('output.mp4')

      if (!outputData) {
        throw new Error('输出文件读取返回 null 或 undefined')
      }

      if (outputData.length === 0) {
        throw new Error('输出文件为空（长度为0）')
      }

      console.log(`输出文件验证成功: ${outputData.length} bytes`)
      return outputData

    } catch (verifyError) {
      console.error('输出文件验证失败:', verifyError)

      // 提供更详细的错误信息
      let errorMessage = '输出文件验证失败'
      if (verifyError && verifyError.message) {
        errorMessage += `: ${verifyError.message}`
      } else if (verifyError) {
        errorMessage += `: ${String(verifyError)}`
      } else {
        errorMessage += ': 未知错误'
      }

      // 尝试获取更多调试信息
      try {
        const files = await this.ffmpeg.listDir('/')
        const fileNames = files.map(file => {
          if (typeof file === 'string') {
            return file
          } else if (file && file.name) {
            return file.name
          } else {
            return JSON.stringify(file)
          }
        })
        console.log('当前文件系统状态:', fileNames)
        errorMessage += ` (文件系统包含: ${fileNames.join(', ')})`
      } catch (debugError) {
        console.warn('无法获取调试信息:', debugError)
      }

      throw new Error(errorMessage)
    }
  }

  /**
   * 生成无音频视频（极简版本）
   */
  async generateVideoWithoutAudio(effect, duration, fps, width, height) {
    console.log('开始生成无音频视频（极简版本）')

    try {
      // 使用最基础的命令，不使用任何复杂参数
      const command = [
        '-loop', '1',
        '-i', 'start.jpg',
        '-t', Math.max(1, Math.min(duration, 10)).toString(), // 限制时长 1-10 秒
        '-vf', 'scale=640:480', // 固定尺寸
        '-r', '15', // 固定帧率
        '-c:v', 'libx264',
        '-pix_fmt', 'yuv420p',
        '-preset', 'ultrafast',
        '-crf', '30', // 较低质量但更稳定
        '-movflags', '+faststart',
        '-avoid_negative_ts', 'make_zero',
        '-y',
        'output.mp4'
      ]

      console.log('执行极简命令:', command.join(' '))

      // 执行命令前再次确认输入文件
      try {
        const inputData = await this.ffmpeg.readFile('start.jpg')
        console.log(`输入文件确认: start.jpg (${inputData.length} bytes)`)
      } catch (inputError) {
        throw new Error(`输入文件不存在: ${inputError.message}`)
      }

      // 执行 FFmpeg 命令
      await this.ffmpeg.exec(command)
      console.log('极简命令执行完成')

      // 立即检查输出文件
      let outputData
      try {
        outputData = await this.ffmpeg.readFile('output.mp4')
        if (!outputData || outputData.length === 0) {
          throw new Error('输出文件为空')
        }
        console.log(`极简视频生成成功: ${outputData.length} bytes`)
        return outputData
      } catch (checkError) {
        console.error('输出文件检查失败:', checkError)

        // 尝试列出文件系统内容进行调试
        try {
          const files = await this.ffmpeg.listDir('/')
          const fileNames = files.map(f => f.name || f.toString())
          console.log('当前文件系统:', fileNames)
        } catch (listError) {
          console.warn('无法列出文件系统:', listError)
        }

        throw new Error(`输出文件检查失败: ${checkError.message || checkError}`)
      }

    } catch (execError) {
      console.error('极简视频生成失败:', execError)
      throw new Error(`极简视频生成失败: ${execError.message || execError}`)
    }
  }

  /**
   * 两步法生成有音频视频
   */
  async generateVideoWithAudioTwoStep(effect, duration, fps, width, height, audioVolume) {
    console.log('使用两步法生成有音频视频')

    // 第一步：生成临时视频
    console.log('第一步：生成临时视频')
    const videoCommand = this.buildTwoStepCommand(effect, duration, fps, width, height, audioVolume)
    await this.ffmpeg.exec(videoCommand)

    // 验证临时视频文件
    try {
      const tempData = await this.ffmpeg.readFile('temp_video.mp4')
      console.log(`临时视频文件大小: ${tempData.length} bytes`)
    } catch (tempError) {
      throw new Error(`临时视频文件生成失败: ${tempError.message}`)
    }

    // 第二步：添加音频
    console.log('第二步：添加音频')
    const audioCommand = this.buildAudioMergeCommand(audioVolume)
    await this.ffmpeg.exec(audioCommand)

    // 清理临时文件
    try {
      await this.ffmpeg.deleteFile('temp_video.mp4')
    } catch (cleanError) {
      console.warn('清理临时视频文件失败:', cleanError)
    }

    console.log('有音频视频生成完成')
  }

  /**
   * 加载 FFmpeg 核心文件
   */
  async loadFFmpegCore() {
    if (this.isElectron) {
      return await this.loadFFmpegCoreForElectron()
    } else {
      return await this.loadFFmpegCoreForBrowser()
    }
  }

  /**
   * 为 Electron 环境加载 FFmpeg 核心
   */
  async loadFFmpegCoreForElectron() {
    console.log('为 Electron 环境加载 FFmpeg 核心文件...')

    // Electron 环境可能需要特殊的加载配置
    const cdnSources = [
      'https://unpkg.com/@ffmpeg/core@0.12.6/dist/esm',
      'https://cdn.jsdelivr.net/npm/@ffmpeg/core@0.12.6/dist/esm',
      'https://fastly.jsdelivr.net/npm/@ffmpeg/core@0.12.6/dist/esm'
    ]

    let lastError = null

    for (const baseURL of cdnSources) {
      try {
        if (this.progressCallback) {
          this.progressCallback({
            type: 'status',
            message: `尝试从 ${baseURL} 加载核心文件 (Electron模式)...`
          })
        }

        // 在 Electron 中，可能需要特殊的加载配置
        const loadConfig = {
          coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
          wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
        }

        // 添加 Electron 特有的配置
        if (this.sharedArrayBufferPolyfilled) {
          console.log('使用 SharedArrayBuffer polyfill 模式加载')
        }

        await this.ffmpeg.load(loadConfig)

        console.log(`从 ${baseURL} 加载成功`)
        return true

      } catch (error) {
        console.warn(`从 ${baseURL} 加载失败:`, error)
        lastError = error
        continue
      }
    }

    console.error('所有 CDN 源都加载失败:', lastError)
    return false
  }

  /**
   * 为浏览器环境加载 FFmpeg 核心
   */
  async loadFFmpegCoreForBrowser() {
    console.log('为浏览器环境加载 FFmpeg 核心文件...')

    const cdnSources = [
      'https://unpkg.com/@ffmpeg/core@0.12.6/dist/esm',
      'https://cdn.jsdelivr.net/npm/@ffmpeg/core@0.12.6/dist/esm',
      'https://fastly.jsdelivr.net/npm/@ffmpeg/core@0.12.6/dist/esm'
    ]

    let lastError = null

    for (const baseURL of cdnSources) {
      try {
        if (this.progressCallback) {
          this.progressCallback({
            type: 'status',
            message: `尝试从 ${baseURL} 加载核心文件...`
          })
        }

        await this.ffmpeg.load({
          coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
          wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
        })

        console.log(`从 ${baseURL} 加载成功`)
        return true

      } catch (error) {
        console.warn(`从 ${baseURL} 加载失败:`, error)
        lastError = error
        continue
      }
    }

    console.error('所有 CDN 源都加载失败:', lastError)
    return false
  }

  /**
   * 获取详细的错误信息
   */
  getDetailedErrorMessage(error) {
    let errorMessage = error.message

    if (this.isElectron) {
      // Electron 环境特有的错误处理
      if (error.message.includes('SharedArrayBuffer')) {
        errorMessage = 'Electron 环境中 SharedArrayBuffer 不可用，已尝试 polyfill 但仍然失败。请确保 Electron 版本支持或联系开发者。'
      } else if (error.message.includes('fetch')) {
        errorMessage = 'Electron 环境中网络请求失败，请检查网络连接或防火墙设置'
      } else if (error.message.includes('WebAssembly')) {
        errorMessage = 'Electron 环境中 WebAssembly 不可用，请升级 Electron 版本'
      } else if (error.message.includes('Worker')) {
        errorMessage = 'Electron 环境中 Web Worker 受限，这可能影响 FFmpeg 性能'
      }
    } else {
      // 浏览器环境的错误处理
      if (error.message.includes('SharedArrayBuffer')) {
        errorMessage = '浏览器不支持SharedArrayBuffer，请确保网站使用HTTPS并设置了正确的安全头'
      } else if (error.message.includes('fetch')) {
        errorMessage = '网络连接问题，无法下载FFmpeg核心文件，请检查网络连接'
      } else if (error.message.includes('WebAssembly')) {
        errorMessage = '浏览器不支持WebAssembly，请使用现代浏览器'
      }
    }

    return errorMessage
  }

  /**
   * 检查浏览器兼容性（支持 Electron 环境）
   */
  checkBrowserCompatibility() {
    const errors = []
    const warnings = []

    console.log('开始兼容性检查...', {
      isElectron: this.isElectron,
      userAgent: navigator.userAgent,
      sharedArrayBufferPolyfilled: this.sharedArrayBufferPolyfilled
    })

    // Electron 环境下的特殊处理
    if (this.isElectron) {
      console.log('Electron 环境检测到，使用特殊兼容性规则')

      // 在 Electron 中，SharedArrayBuffer 可能不可用，但我们可以尝试 polyfill
      if (typeof SharedArrayBuffer === 'undefined' && !this.sharedArrayBufferPolyfilled) {
        warnings.push('SharedArrayBuffer 在 Electron 环境中不可用，已尝试 polyfill')
      }

      // 检查 WebAssembly 支持（这在 Electron 中应该是可用的）
      if (typeof WebAssembly === 'undefined') {
        errors.push('WebAssembly 不支持，请升级 Electron 版本')
      }

      // 检查 Worker 支持（在 Electron 中应该可用）
      if (typeof Worker === 'undefined') {
        warnings.push('Web Worker 在 Electron 环境中可能受限')
      }

      // 检查 URL 支持
      if (typeof URL === 'undefined') {
        errors.push('URL API 不支持')
      }

      // Electron 版本检查
      try {
        if (typeof process !== 'undefined' && process.versions?.electron) {
          const electronVersion = process.versions.electron
          console.log(`Electron 版本: ${electronVersion}`)

          // 检查 Electron 版本是否足够新
          const majorVersion = parseInt(electronVersion.split('.')[0])
          if (majorVersion < 10) {
            warnings.push(`Electron 版本较低 (${electronVersion})，建议升级到 10.0+`)
          }
        }
      } catch (e) {
        console.warn('无法获取 Electron 版本信息:', e)
      }

    } else {
      // 普通浏览器环境的检查
      console.log('普通浏览器环境，使用标准兼容性规则')

      // 检查SharedArrayBuffer支持
      if (typeof SharedArrayBuffer === 'undefined') {
        errors.push('SharedArrayBuffer不可用，请确保网站使用HTTPS并设置了正确的CORS头')
      }

      // 检查WebAssembly支持
      if (typeof WebAssembly === 'undefined') {
        errors.push('WebAssembly不支持，请使用现代浏览器')
      }

      // 检查Worker支持
      if (typeof Worker === 'undefined') {
        errors.push('Web Worker不支持')
      }

      // 检查URL支持
      if (typeof URL === 'undefined') {
        errors.push('URL API不支持')
      }

      // 检查浏览器版本
      const userAgent = navigator.userAgent
      const isChrome = /Chrome\/(\d+)/.test(userAgent)
      const isFirefox = /Firefox\/(\d+)/.test(userAgent)
      const isSafari = /Safari\//.test(userAgent) && !/Chrome/.test(userAgent)

      if (isChrome) {
        const version = parseInt(userAgent.match(/Chrome\/(\d+)/)[1])
        if (version < 67) {
          errors.push(`Chrome版本过低 (${version})，需要67+`)
        }
      } else if (isFirefox) {
        const version = parseInt(userAgent.match(/Firefox\/(\d+)/)[1])
        if (version < 79) {
          errors.push(`Firefox版本过低 (${version})，需要79+`)
        }
      } else if (isSafari) {
        warnings.push('Safari浏览器可能不完全支持，建议使用Chrome或Firefox')
      }
    }

    const result = {
      compatible: errors.length === 0,
      errors,
      warnings,
      isElectron: this.isElectron,
      sharedArrayBufferPolyfilled: this.sharedArrayBufferPolyfilled
    }

    if (errors.length > 0) {
      console.warn('兼容性检查失败:', result)
    } else if (warnings.length > 0) {
      console.warn('兼容性检查通过，但有警告:', result)
    } else {
      console.log('兼容性检查完全通过:', result)
    }

    return result
  }

  /**
   * 生成过渡视频（支持背景音乐）
   */
  async generateTransitionVideo(startImageFile, endImageFile, options = {}) {
    if (!this.isLoaded) {
      throw new Error('FFmpeg未初始化，请先调用initialize()')
    }

    const {
      duration = 5,
      effect = 'fade',
      fps = 30,
      width = 1280,
      height = 720,
      enableAudio = false,
      audioFile = null,
      audioVolume = 0.5
    } = options

    try {
      // 清理之前的文件
      await this.cleanup()

      if (this.progressCallback) {
        this.progressCallback({
          type: 'status',
          message: '准备图片文件...'
        })
      }

      // 写入输入文件
      try {
        console.log('写入图片文件...')
        const startData = await fetchFile(startImageFile)
        const endData = await fetchFile(endImageFile)

        console.log(`图片数据大小: start=${startData.length} bytes, end=${endData.length} bytes`)

        await this.ffmpeg.writeFile('start.jpg', startData)
        await this.ffmpeg.writeFile('end.jpg', endData)

        console.log('图片文件写入成功')
      } catch (writeError) {
        console.error('图片文件写入失败:', writeError)
        throw new Error(`无法写入图片文件: ${writeError.message}`)
      }

      // 处理音频文件
      let hasAudio = false
      if (enableAudio) {
        hasAudio = await this.prepareAudioFile(audioFile, duration)
      }

      if (this.progressCallback) {
        this.progressCallback({
          type: 'status',
          message: hasAudio ? '生成带音频的过渡效果...' : '生成过渡效果...'
        })
      }

      // 执行视频生成
      try {
        await this.executeVideoGeneration(effect, duration, fps, width, height, hasAudio, audioVolume)
      } catch (execError) {
        console.error('视频生成失败:', execError)

        // 如果是音频相关错误，尝试生成无音频视频
        if (hasAudio) {
          console.log('尝试生成无音频视频作为降级方案')
          try {
            await this.executeVideoGeneration(effect, duration, fps, width, height, false, audioVolume)
            hasAudio = false // 更新状态
            console.log('无音频视频生成成功')
          } catch (noAudioError) {
            console.error('无音频视频生成也失败:', noAudioError)
            throw new Error(`视频生成完全失败: ${noAudioError.message}`)
          }
        } else {
          throw execError
        }
      }

      if (this.progressCallback) {
        this.progressCallback({
          type: 'status',
          message: '读取生成的视频...'
        })
      }

      // 读取输出文件
      let data
      try {
        // 使用验证方法读取输出文件
        console.log('使用验证方法读取输出文件')
        data = await this.verifyOutputFile()
      } catch (readError) {
        console.error('读取输出文件失败:', readError)
        throw new Error(`无法读取生成的视频文件: ${readError.message || readError}`)
      }

      // 创建Blob URL
      const blob = new Blob([data.buffer], { type: 'video/mp4' })
      const videoUrl = URL.createObjectURL(blob)

      if (this.progressCallback) {
        this.progressCallback({
          type: 'status',
          message: hasAudio ? '带音频视频生成完成！' : '视频生成完成！'
        })
      }

      return {
        videoUrl,
        blob,
        size: blob.size,
        hasAudio
      }
    } catch (error) {
      console.error('视频生成失败:', error)
      throw error
    }
  }

  /**
   * 准备音频文件
   */
  async prepareAudioFile(audioFile, videoDuration) {
    try {
      console.log('开始准备音频文件...')

      if (this.progressCallback) {
        this.progressCallback({
          type: 'status',
          message: '准备音频文件...'
        })
      }

      let audioData
      if (audioFile) {
        // 使用用户提供的音频文件
        console.log('使用自定义音频文件:', audioFile.name)
        audioData = await fetchFile(audioFile)
      } else {
        // 使用默认的背景音乐
        console.log('使用默认背景音乐')
        audioData = await this.loadDefaultAudio()
      }

      if (!audioData) {
        console.warn('音频文件加载失败，将生成无音频视频')
        return false
      }

      console.log(`音频数据大小: ${audioData.length} bytes`)

      // 写入音频文件
      try {
        await this.ffmpeg.writeFile('background.mp3', audioData)
        console.log('音频文件写入成功')
      } catch (writeError) {
        console.error('音频文件写入失败:', writeError)
        return false
      }

      if (this.progressCallback) {
        this.progressCallback({
          type: 'status',
          message: '处理音频长度...'
        })
      }

      // 获取音频信息
      const audioInfo = await this.getAudioInfo('background.mp3')
      console.log('音频信息:', audioInfo)

      // 根据视频长度调整音频
      try {
        await this.adjustAudioLength('background.mp3', 'audio_adjusted.mp3', videoDuration, audioInfo.duration)
        console.log('音频长度调整完成')

        // 验证调整后的音频文件是否存在
        try {
          const adjustedData = await this.ffmpeg.readFile('audio_adjusted.mp3')
          console.log(`调整后音频文件大小: ${adjustedData.length} bytes`)
        } catch (verifyError) {
          console.error('调整后的音频文件验证失败:', verifyError)
          return false
        }

        return true
      } catch (adjustError) {
        console.error('音频长度调整失败:', adjustError)
        return false
      }

    } catch (error) {
      console.error('音频处理失败:', error)
      if (this.progressCallback) {
        this.progressCallback({
          type: 'status',
          message: '音频处理失败，将生成无音频视频'
        })
      }
      return false
    }
  }

  /**
   * 加载默认音频文件
   */
  async loadDefaultAudio() {
    try {
      // 在不同环境中加载音频文件
      if (this.isElectron) {
        return await this.loadAudioInElectron()
      } else {
        return await this.loadAudioInBrowser()
      }
    } catch (error) {
      console.error('加载默认音频失败:', error)
      return null
    }
  }

  /**
   * 在 Electron 环境中加载音频
   */
  async loadAudioInElectron() {
    try {
      // 尝试通过 IPC 读取音频文件
      if (window.electron?.ipcRenderer) {
        const audioPath = await window.electron.ipcRenderer.invoke('app:getAssetPath', 'mp1.mp3')
        const audioData = await window.electron.ipcRenderer.invoke('fs:readFile', audioPath)
        return new Uint8Array(audioData)
      }

      // 降级到浏览器方式
      return await this.loadAudioInBrowser()
    } catch (error) {
      console.warn('Electron 音频加载失败，尝试浏览器方式:', error)
      return await this.loadAudioInBrowser()
    }
  }

  /**
   * 在浏览器环境中加载音频
   */
  async loadAudioInBrowser() {
    try {
      // 动态导入音频文件
      const audioModule = await import('@/assets/mp1.mp3')
      const audioUrl = audioModule.default || audioModule
      return await fetchFile(audioUrl)
    } catch (error) {
      console.error('浏览器音频加载失败:', error)
      return null
    }
  }

  /**
   * 获取音频信息
   */
  async getAudioInfo(audioFileName) {
    try {
      console.log(`获取音频信息: ${audioFileName}`)

      // 检查文件是否存在
      try {
        const fileData = await this.ffmpeg.readFile(audioFileName)
        console.log(`音频文件大小: ${fileData.length} bytes`)
      } catch (fileError) {
        console.error('音频文件不存在:', fileError)
        throw new Error(`音频文件不存在: ${audioFileName}`)
      }

      // 由于 FFmpeg.js 无法直接获取音频时长，使用估算方法
      // 根据文件大小估算时长（这是一个粗略的估算）
      const fileData = await this.ffmpeg.readFile(audioFileName)
      const fileSizeKB = fileData.length / 1024

      // 假设 128kbps 的音频，估算时长
      const estimatedDuration = Math.max(30, Math.min(300, fileSizeKB / 16)) // 30秒到5分钟之间

      console.log(`估算音频时长: ${estimatedDuration}秒`)

      return {
        duration: estimatedDuration,
        format: 'mp3'
      }
    } catch (error) {
      console.warn('获取音频信息失败，使用默认值:', error)
      return {
        duration: 120, // 默认2分钟
        format: 'mp3'
      }
    }
  }

  /**
   * 调整音频长度
   */
  async adjustAudioLength(inputAudio, outputAudio, targetDuration, audioDuration) {
    try {
      console.log(`调整音频长度: 目标=${targetDuration}s, 原始=${audioDuration}s`)

      // 确保目标时长是有效的数字
      const duration = Math.max(1, Math.floor(targetDuration))

      if (audioDuration <= targetDuration) {
        // 音频比视频短，需要循环
        const loopCount = Math.max(1, Math.ceil(targetDuration / audioDuration))
        console.log(`音频循环 ${loopCount} 次`)

        await this.ffmpeg.exec([
          '-stream_loop', Math.max(0, loopCount - 1).toString(),
          '-i', inputAudio,
          '-t', duration.toString(),
          '-c:a', 'aac',
          '-b:a', '128k',
          '-ar', '44100',
          '-y', // 覆盖输出文件
          outputAudio
        ])
      } else {
        // 音频比视频长，需要截取
        console.log('截取音频前段')

        await this.ffmpeg.exec([
          '-i', inputAudio,
          '-t', duration.toString(),
          '-c:a', 'aac',
          '-b:a', '128k',
          '-ar', '44100',
          '-y', // 覆盖输出文件
          outputAudio
        ])
      }

      console.log('音频长度调整完成')
    } catch (error) {
      console.error('调整音频长度失败:', error)

      // 如果调整失败，尝试简单复制
      try {
        console.log('尝试简单复制音频文件')
        await this.ffmpeg.exec([
          '-i', inputAudio,
          '-c:a', 'aac',
          '-b:a', '128k',
          '-ar', '44100',
          '-y',
          outputAudio
        ])
        console.log('音频文件复制成功')
      } catch (copyError) {
        console.error('音频文件复制也失败:', copyError)
        throw new Error(`音频处理完全失败: ${copyError.message}`)
      }
    }
  }

  /**
   * 构建FFmpeg命令（支持音频，简化版本）
   */
  buildFFmpegCommand(effect, duration, fps, width, height, hasAudio = false, audioVolume = 0.5) {
    console.log(`构建FFmpeg命令: effect=${effect}, duration=${duration}, hasAudio=${hasAudio}`)

    // 使用简化的命令结构来避免复杂滤镜错误
    if (hasAudio) {
      return this.buildCommandWithAudio(effect, duration, fps, width, height, audioVolume)
    } else {
      return this.buildCommandWithoutAudio(effect, duration, fps, width, height)
    }
  }

  /**
   * 构建无音频的简化命令
   */
  buildCommandWithoutAudio(effect, duration, fps, width, height) {
    console.log('构建无音频命令')

    // 使用最基础的命令，避免任何可能的问题
    const command = [
      '-f', 'image2',
      '-loop', '1',
      '-i', 'start.jpg',
      '-t', duration.toString(),
      '-r', fps.toString(),
      '-vf', `scale=${width}:${height}:force_original_aspect_ratio=decrease,pad=${width}:${height}:(ow-iw)/2:(oh-ih)/2`,
      '-c:v', 'libx264',
      '-pix_fmt', 'yuv420p',
      '-preset', 'ultrafast',
      '-movflags', '+faststart',
      '-y',
      'output.mp4'
    ]

    console.log('基础无音频命令:', command.join(' '))
    return command
  }

  /**
   * 构建有音频的简化命令
   */
  buildCommandWithAudio(effect, duration, fps, width, height, audioVolume) {
    console.log('构建有音频命令')

    // 使用两步法：先生成视频，再添加音频
    return this.buildTwoStepCommand(effect, duration, fps, width, height, audioVolume)
  }

  /**
   * 两步法构建命令：先生成视频，再添加音频
   */
  buildTwoStepCommand(effect, duration, fps, width, height, audioVolume) {
    // 第一步：生成无音频视频
    const videoCommand = [
      '-loop', '1', '-i', 'start.jpg',
      '-loop', '1', '-i', 'end.jpg',
      '-filter_complex',
      `[0:v]scale=${width}:${height}[v0];[1:v]scale=${width}:${height}[v1];[v0][v1]blend=all_expr='A*(1-T/${duration})+B*(T/${duration})':shortest=1`,
      '-t', duration.toString(),
      '-r', fps.toString(),
      '-c:v', 'libx264',
      '-pix_fmt', 'yuv420p',
      '-preset', 'ultrafast',
      '-y',
      'temp_video.mp4'
    ]

    console.log('第一步视频命令:', videoCommand.join(' '))
    return videoCommand
  }

  /**
   * 第二步：添加音频到视频
   */
  buildAudioMergeCommand(audioVolume) {
    const audioCommand = [
      '-i', 'temp_video.mp4',
      '-i', 'audio_adjusted.mp3',
      '-filter_complex',
      `[1:a]volume=${audioVolume}[audio]`,
      '-map', '0:v',
      '-map', '[audio]',
      '-c:v', 'copy', // 复制视频流，不重新编码
      '-c:a', 'aac',
      '-b:a', '128k',
      '-shortest', // 以最短流为准
      '-y',
      'output.mp4'
    ]

    console.log('第二步音频命令:', audioCommand.join(' '))
    return audioCommand
  }

  /**
   * 清理临时文件
   */
  async cleanup() {
    if (!this.ffmpeg) return

    const files = ['start.jpg', 'end.jpg', 'output.mp4', 'background.mp3', 'audio_adjusted.mp3', 'temp_video.mp4']
    for (const file of files) {
      try {
        await this.ffmpeg.deleteFile(file)
        console.debug(`清理文件成功: ${file}`)
      } catch (error) {
        // 文件可能不存在，忽略错误
        console.debug(`清理文件失败 ${file}:`, error.message)
      }
    }
  }

  /**
   * 测试基础 FFmpeg 功能
   */
  async testBasicFunctionality() {
    if (!this.isLoaded) {
      throw new Error('FFmpeg 未初始化')
    }

    try {
      console.log('开始测试基础 FFmpeg 功能...')

      // 创建一个简单的测试图片数据
      const canvas = document.createElement('canvas')
      canvas.width = 640
      canvas.height = 480
      const ctx = canvas.getContext('2d')

      // 绘制一个简单的红色矩形
      ctx.fillStyle = 'red'
      ctx.fillRect(0, 0, 640, 480)

      // 转换为 blob
      const blob = await new Promise(resolve => canvas.toBlob(resolve, 'image/jpeg'))
      const imageData = new Uint8Array(await blob.arrayBuffer())

      // 写入测试图片
      await this.ffmpeg.writeFile('test.jpg', imageData)
      console.log('测试图片写入成功')

      // 执行最简单的命令
      const testCommand = [
        '-i', 'test.jpg',
        '-t', '1',
        '-r', '1',
        '-c:v', 'libx264',
        '-pix_fmt', 'yuv420p',
        '-preset', 'ultrafast',
        '-y',
        'test_output.mp4'
      ]

      console.log('执行测试命令:', testCommand.join(' '))
      await this.ffmpeg.exec(testCommand)

      // 检查输出
      const outputData = await this.ffmpeg.readFile('test_output.mp4')
      console.log(`测试输出文件大小: ${outputData.length} bytes`)

      // 清理测试文件
      await this.ffmpeg.deleteFile('test.jpg')
      await this.ffmpeg.deleteFile('test_output.mp4')

      console.log('基础功能测试成功')
      return true

    } catch (error) {
      console.error('基础功能测试失败:', error)
      throw error
    }
  }

  /**
   * 终止FFmpeg进程
   */
  async terminate() {
    if (this.ffmpeg) {
      await this.ffmpeg.terminate()
      this.ffmpeg = null
      this.isLoaded = false
      this.isLoading = false
    }
  }

  /**
   * 获取FFmpeg版本信息
   */
  async getVersion() {
    if (!this.isLoaded) {
      throw new Error('FFmpeg未初始化')
    }
    
    await this.ffmpeg.exec(['-version'])
    return 'FFmpeg.js WebAssembly版本'
  }
}

// 创建单例实例
const ffmpegHelper = new FFmpegHelper()

export default ffmpegHelper
