/**
 * Electron 文件处理工具
 * 提供 Electron 和浏览器环境的统一文件处理接口
 */

class ElectronFileHandler {
  constructor() {
    this.isElectron = this.checkElectronEnvironment()
    this.electronAPI = this.isElectron ? window.electron : null
  }

  /**
   * 检测是否在 Electron 环境中运行
   */
  checkElectronEnvironment() {
    // 检查多种 Electron 环境标识
    return !!(
      typeof window !== 'undefined' && 
      (
        window.electron || 
        window.electronAPI ||
        window.electronEnvironment ||
        navigator.userAgent.toLowerCase().indexOf('electron') > -1 ||
        (typeof process !== 'undefined' && process.versions?.electron)
      )
    )
  }

  /**
   * 统一的文件选择接口
   * @param {Object} options - 文件选择选项
   * @returns {Promise<Array>} 选择的文件列表
   */
  async selectFiles(options = {}) {
    const defaultOptions = {
      multiple: false,
      accept: 'image/*',
      filters: [
        { name: '图片文件', extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'] },
        { name: '所有文件', extensions: ['*'] }
      ]
    }

    const finalOptions = { ...defaultOptions, ...options }

    if (this.isElectron && this.electronAPI?.ipcRenderer) {
      return await this.selectFilesElectron(finalOptions)
    } else {
      return await this.selectFilesBrowser(finalOptions)
    }
  }

  /**
   * Electron 环境下的文件选择
   */
  async selectFilesElectron(options) {
    try {
      console.log('[ElectronFileHandler] 使用 Electron 文件选择')
      
      // 通过 IPC 调用主进程的文件对话框
      const result = await this.electronAPI.ipcRenderer.invoke('dialog:showOpenDialog', {
        properties: options.multiple ? ['openFile', 'multiSelections'] : ['openFile'],
        filters: options.filters
      })

      if (result.canceled || !result.filePaths?.length) {
        return []
      }

      // 将文件路径转换为文件对象
      const files = []
      for (const filePath of result.filePaths) {
        try {
          const file = await this.createFileFromPath(filePath)
          files.push(file)
        } catch (error) {
          console.error(`读取文件失败: ${filePath}`, error)
          throw new Error(`无法读取文件: ${filePath}`)
        }
      }

      return files
    } catch (error) {
      console.error('Electron 文件选择失败:', error)
      // 降级到浏览器文件选择
      console.log('[ElectronFileHandler] 降级到浏览器文件选择')
      return await this.selectFilesBrowser(options)
    }
  }

  /**
   * 浏览器环境下的文件选择
   */
  async selectFilesBrowser(options) {
    return new Promise((resolve, reject) => {
      const input = document.createElement('input')
      input.type = 'file'
      input.multiple = options.multiple
      
      // 设置文件类型过滤
      if (options.accept) {
        input.accept = options.accept
      } else if (options.filters?.length > 0) {
        // 将 Electron 的 filters 转换为浏览器的 accept
        const extensions = options.filters
          .flatMap(filter => filter.extensions)
          .filter(ext => ext !== '*')
          .map(ext => `.${ext}`)
          .join(',')
        input.accept = extensions
      }

      input.onchange = (event) => {
        const files = Array.from(event.target.files || [])
        resolve(files)
      }

      input.oncancel = () => {
        resolve([])
      }

      input.onerror = (error) => {
        reject(new Error(`文件选择失败: ${error.message}`))
      }

      input.click()
    })
  }

  /**
   * 从文件路径创建 File 对象（Electron 环境）
   */
  async createFileFromPath(filePath) {
    try {
      // 通过 IPC 读取文件内容
      const buffer = await this.electronAPI.ipcRenderer.invoke('fs:readFile', filePath)
      const uint8Array = new Uint8Array(buffer)

      // 获取文件名和扩展名
      const fileName = filePath.split(/[/\\]/).pop()
      const mimeType = this.getMimeTypeFromExtension(fileName)

      // 获取文件信息
      const fileInfo = await this.electronAPI.ipcRenderer.invoke('fs:getFileInfo', filePath)

      // 创建 File 对象
      const file = new File([uint8Array], fileName, {
        type: mimeType,
        lastModified: fileInfo.mtime || Date.now()
      })

      // 添加 Electron 特有的属性
      file.electronPath = filePath
      file.isElectronFile = true

      return file
    } catch (error) {
      console.error('创建文件对象失败:', error)
      throw new Error(`无法创建文件对象: ${error.message}`)
    }
  }

  /**
   * 根据文件扩展名获取 MIME 类型
   */
  getMimeTypeFromExtension(fileName) {
    const extension = fileName.split('.').pop()?.toLowerCase()
    const mimeTypes = {
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'bmp': 'image/bmp',
      'webp': 'image/webp',
      'svg': 'image/svg+xml'
    }
    return mimeTypes[extension] || 'application/octet-stream'
  }

  /**
   * 创建文件预览URL
   * @param {File|string} file - 文件对象或文件路径
   * @returns {string} 预览URL
   */
  createPreviewUrl(file) {
    if (!file) return ''

    // 如果是字符串路径
    if (typeof file === 'string') {
      if (file.startsWith('blob:') || file.startsWith('data:') || file.startsWith('http')) {
        return file
      }
      
      // Electron 环境下的文件路径
      if (this.isElectron && (file.startsWith('/') || file.match(/^[A-Z]:\\/))) {
        return `file://${file.replace(/\\/g, '/')}`
      }
      
      return file
    }

    // 如果是 File 对象
    if (file instanceof File) {
      // Electron 文件，优先使用文件路径
      if (file.isElectronFile && file.electronPath) {
        return `file://${file.electronPath.replace(/\\/g, '/')}`
      }
      
      // 普通浏览器文件，创建 blob URL
      return URL.createObjectURL(file)
    }

    return ''
  }

  /**
   * 释放预览URL资源
   */
  revokePreviewUrl(url) {
    if (url && url.startsWith('blob:')) {
      URL.revokeObjectURL(url)
    }
    // file:// 协议的URL不需要释放
  }

  /**
   * 验证文件类型
   */
  validateFileType(file, allowedTypes = ['image/jpeg', 'image/png', 'image/gif']) {
    if (!file) return false
    
    // 检查 MIME 类型
    if (allowedTypes.includes(file.type)) {
      return true
    }
    
    // 如果 MIME 类型检查失败，检查文件扩展名
    if (file.name) {
      const extension = file.name.split('.').pop()?.toLowerCase()
      const extensionMap = {
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'png': 'image/png',
        'gif': 'image/gif',
        'bmp': 'image/bmp',
        'webp': 'image/webp'
      }
      
      const mimeType = extensionMap[extension]
      return mimeType && allowedTypes.includes(mimeType)
    }
    
    return false
  }

  /**
   * 验证文件大小
   */
  validateFileSize(file, maxSize = 10 * 1024 * 1024) {
    return file && file.size <= maxSize
  }

  /**
   * 获取文件信息
   */
  getFileInfo(file) {
    if (!file) return null

    return {
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: file.lastModified,
      isElectronFile: file.isElectronFile || false,
      electronPath: file.electronPath || null
    }
  }
}

// 创建单例实例
const electronFileHandler = new ElectronFileHandler()

export default electronFileHandler
