<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>诊断工具测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .result {
            background: #f5f5f5;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .good { background: #d4edda; }
        .warning { background: #fff3cd; }
        .error { background: #f8d7da; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>视频生成功能诊断工具测试</h1>
    
    <button onclick="runTest()">运行诊断测试</button>
    
    <div id="results"></div>

    <script type="module">
        // 简化版的诊断工具，用于测试
        class TestDiagnosticTool {
            static async runDiagnostics() {
                console.log('开始运行诊断检查...')
                
                const results = {
                    browser: this.checkBrowser(),
                    security: this.checkSecurityHeaders(),
                    memory: this.checkMemory(),
                    network: await this.checkNetwork(),
                    webassembly: this.checkWebAssembly(),
                    workers: this.checkWorkers()
                }

                const overall = this.calculateOverallStatus(results)
                const recommendations = this.generateRecommendations(results)

                const diagnostics = {
                    overall,
                    details: results,
                    recommendations
                }

                console.log('诊断检查完成:', diagnostics)
                return diagnostics
            }

            static checkBrowser() {
                const userAgent = navigator.userAgent
                const isChrome = /Chrome\/(\d+)/.test(userAgent)
                const isFirefox = /Firefox\/(\d+)/.test(userAgent)
                const isSafari = /Safari\//.test(userAgent) && !/Chrome/.test(userAgent)
                const isEdge = /Edg\/(\d+)/.test(userAgent)

                let browserInfo = { name: 'Unknown', version: 0, supported: false, status: 'error' }

                if (isChrome) {
                    const version = parseInt(userAgent.match(/Chrome\/(\d+)/)[1])
                    const supported = version >= 67
                    browserInfo = { name: 'Chrome', version, supported, status: supported ? 'good' : 'error' }
                } else if (isFirefox) {
                    const version = parseInt(userAgent.match(/Firefox\/(\d+)/)[1])
                    const supported = version >= 79
                    browserInfo = { name: 'Firefox', version, supported, status: supported ? 'good' : 'error' }
                } else if (isEdge) {
                    const version = parseInt(userAgent.match(/Edg\/(\d+)/)[1])
                    const supported = version >= 79
                    browserInfo = { name: 'Edge', version, supported, status: supported ? 'good' : 'error' }
                } else if (isSafari) {
                    browserInfo = { name: 'Safari', version: 0, supported: false, status: 'warning' }
                }

                console.log('浏览器检查结果:', browserInfo)
                return browserInfo
            }

            static checkSecurityHeaders() {
                const hasSharedArrayBuffer = typeof SharedArrayBuffer !== 'undefined'
                const isSecureContext = window.isSecureContext
                const isHTTPS = location.protocol === 'https:'
                const isLocalhost = location.hostname === 'localhost' || location.hostname === '127.0.0.1'

                const result = {
                    sharedArrayBuffer: hasSharedArrayBuffer,
                    secureContext: isSecureContext,
                    https: isHTTPS,
                    localhost: isLocalhost,
                    status: hasSharedArrayBuffer ? 'good' : 'error'
                }

                console.log('安全检查结果:', result)
                return result
            }

            static checkMemory() {
                const memory = performance.memory
                if (!memory) {
                    console.log('内存检查结果: 不可用')
                    return { available: false, status: 'unknown' }
                }

                const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024)
                const totalMB = Math.round(memory.totalJSHeapSize / 1024 / 1024)
                const limitMB = Math.round(memory.jsHeapSizeLimit / 1024 / 1024)

                const result = {
                    available: true,
                    used: usedMB,
                    total: totalMB,
                    limit: limitMB,
                    status: limitMB > 1000 ? 'good' : 'warning'
                }

                console.log('内存检查结果:', result)
                return result
            }

            static async checkNetwork() {
                const testUrls = [
                    'https://unpkg.com/@ffmpeg/core@0.12.6/dist/esm/ffmpeg-core.js',
                    'https://cdn.jsdelivr.net/npm/@ffmpeg/core@0.12.6/dist/esm/ffmpeg-core.js'
                ]

                const results = []
                for (const url of testUrls) {
                    try {
                        const start = Date.now()
                        const controller = new AbortController()
                        const timeoutId = setTimeout(() => controller.abort(), 5000) // 5秒超时
                        
                        const response = await fetch(url, { 
                            method: 'HEAD',
                            signal: controller.signal
                        })
                        
                        clearTimeout(timeoutId)
                        const time = Date.now() - start
                        
                        results.push({
                            url,
                            status: response.ok ? 'success' : 'error',
                            time,
                            statusCode: response.status
                        })
                    } catch (error) {
                        results.push({
                            url,
                            status: 'error',
                            time: -1,
                            error: error.name === 'AbortError' ? '请求超时' : error.message
                        })
                    }
                }

                const hasSuccess = results.some(r => r.status === 'success')
                console.log('网络检查结果:', { results, hasSuccess })

                return {
                    tests: results,
                    status: hasSuccess ? 'good' : 'error'
                }
            }

            static checkWebAssembly() {
                const hasWebAssembly = typeof WebAssembly !== 'undefined'
                let instantiateSupport = false
                let compileSupport = false

                if (hasWebAssembly) {
                    try {
                        instantiateSupport = typeof WebAssembly.instantiate === 'function'
                        compileSupport = typeof WebAssembly.compile === 'function'
                    } catch (e) {
                        console.warn('WebAssembly 功能检查失败:', e)
                    }
                }

                const result = {
                    available: hasWebAssembly,
                    instantiate: instantiateSupport,
                    compile: compileSupport,
                    status: hasWebAssembly && instantiateSupport && compileSupport ? 'good' : 'error'
                }

                console.log('WebAssembly 检查结果:', result)
                return result
            }

            static checkWorkers() {
                const hasWorker = typeof Worker !== 'undefined'
                const hasSharedWorker = typeof SharedWorker !== 'undefined'

                const result = {
                    worker: hasWorker,
                    sharedWorker: hasSharedWorker,
                    status: hasWorker ? 'good' : 'error'
                }

                console.log('Workers 检查结果:', result)
                return result
            }

            static calculateOverallStatus(results) {
                const criticalChecks = ['browser', 'security', 'webassembly', 'workers']
                const errors = criticalChecks.filter(check => {
                    const result = results[check]
                    if (check === 'browser') {
                        return !result.supported
                    }
                    return result.status === 'error'
                })

                console.log('诊断检查结果:', {
                    criticalChecks,
                    results: criticalChecks.map(check => ({
                        check,
                        result: results[check],
                        hasError: check === 'browser' ? !results[check].supported : results[check].status === 'error'
                    })),
                    errors,
                    errorCount: errors.length
                })

                if (errors.length === 0) return 'good'
                if (errors.length <= 2) return 'warning'
                return 'error'
            }

            static generateRecommendations(results) {
                const recommendations = []

                if (!results.browser.supported) {
                    recommendations.push(`请升级${results.browser.name}浏览器到最新版本`)
                }

                if (!results.security.sharedArrayBuffer) {
                    if (!results.security.https) {
                        recommendations.push('请使用HTTPS协议访问网站')
                    } else {
                        recommendations.push('请确保服务器设置了正确的CORS安全头')
                    }
                }

                if (results.memory.status === 'warning') {
                    recommendations.push('浏览器可用内存较少，可能影响视频处理性能')
                }

                if (results.network.status === 'error') {
                    recommendations.push('网络连接问题，无法下载FFmpeg核心文件')
                }

                if (!results.webassembly.available) {
                    recommendations.push('浏览器不支持WebAssembly，请使用现代浏览器')
                }

                if (!results.workers.worker) {
                    recommendations.push('浏览器不支持Web Worker，请使用现代浏览器')
                }

                return recommendations
            }
        }

        window.runTest = async function() {
            const resultsDiv = document.getElementById('results')
            resultsDiv.innerHTML = '<p>正在运行诊断...</p>'
            
            try {
                const diagnostics = await TestDiagnosticTool.runDiagnostics()
                
                let html = `
                    <div class="result ${diagnostics.overall}">
                        <h3>总体状态: ${diagnostics.overall}</h3>
                    </div>
                `
                
                for (const [key, result] of Object.entries(diagnostics.details)) {
                    html += `
                        <div class="result ${result.status}">
                            <h4>${key}</h4>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `
                }
                
                if (diagnostics.recommendations.length > 0) {
                    html += `
                        <div class="result">
                            <h4>建议</h4>
                            <ul>
                                ${diagnostics.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                            </ul>
                        </div>
                    `
                }
                
                resultsDiv.innerHTML = html
            } catch (error) {
                resultsDiv.innerHTML = `<div class="result error"><h3>诊断失败: ${error.message}</h3></div>`
            }
        }
    </script>
</body>
</html>
