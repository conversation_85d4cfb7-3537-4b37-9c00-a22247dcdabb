<template>
  <div class="electron-ffmpeg-test">
    <h3>Electron FFmpeg 测试组件</h3>
    
    <!-- 环境信息 -->
    <div class="environment-info">
      <h4>环境信息</h4>
      <ul>
        <li>运行环境: {{ environmentInfo.isElectron ? 'Electron' : '浏览器' }}</li>
        <li v-if="environmentInfo.isElectron">Electron 版本: {{ environmentInfo.electronVersion }}</li>
        <li v-if="environmentInfo.isElectron">平台: {{ environmentInfo.platform }}</li>
        <li>SharedArrayBuffer: {{ environmentInfo.sharedArrayBufferAvailable ? '可用' : '不可用' }}</li>
        <li>WebAssembly: {{ environmentInfo.webAssemblyAvailable ? '可用' : '不可用' }}</li>
      </ul>
    </div>

    <!-- FFmpeg 状态 -->
    <div class="ffmpeg-status">
      <h4>FFmpeg 状态</h4>
      <div class="status-item">
        <span>初始化状态: </span>
        <span :class="ffmpegStatus.class">{{ ffmpegStatus.text }}</span>
      </div>
      <div v-if="ffmpegError" class="error-message">
        错误信息: {{ ffmpegError }}
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="actions">
      <el-button 
        type="primary" 
        @click="initializeFFmpeg"
        :loading="isInitializing"
        :disabled="isFFmpegLoaded"
      >
        {{ isFFmpegLoaded ? 'FFmpeg 已就绪' : '初始化 FFmpeg' }}
      </el-button>
      
      <el-button 
        type="success" 
        @click="selectFiles"
        :disabled="!isFFmpegLoaded"
      >
        选择图片文件
      </el-button>
      
      <el-button 
        type="warning" 
        @click="generateTestVideo"
        :disabled="!canGenerateVideo"
        :loading="isGenerating"
      >
        生成测试视频
      </el-button>
    </div>

    <!-- 文件信息 -->
    <div v-if="selectedFiles.length > 0" class="file-info">
      <h4>选择的文件</h4>
      <div v-for="(file, index) in selectedFiles" :key="index" class="file-item">
        <span>{{ file.name }}</span>
        <span>({{ formatFileSize(file.size) }})</span>
        <span v-if="file.isElectronFile">[Electron文件]</span>
      </div>
    </div>

    <!-- 进度信息 -->
    <div v-if="progressInfo.visible" class="progress-info">
      <h4>处理进度</h4>
      <el-progress 
        :percentage="progressInfo.percentage" 
        :status="progressInfo.status"
      />
      <div class="progress-text">{{ progressInfo.text }}</div>
    </div>

    <!-- 生成的视频 -->
    <div v-if="generatedVideoUrl" class="generated-video">
      <h4>生成的视频</h4>
      <video 
        :src="generatedVideoUrl" 
        controls 
        width="400" 
        height="300"
      />
      <div class="video-actions">
        <el-button @click="downloadVideo">下载视频</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import ffmpegHelper from '@/utils/ffmpegHelper'
import electronFileHandler from '@/utils/electronFileHandler'

export default {
  name: 'ElectronFFmpegTest',
  setup() {
    // 响应式数据
    const environmentInfo = reactive({
      isElectron: false,
      electronVersion: '',
      platform: '',
      sharedArrayBufferAvailable: false,
      webAssemblyAvailable: false
    })

    const isInitializing = ref(false)
    const isFFmpegLoaded = ref(false)
    const ffmpegError = ref('')
    const selectedFiles = ref([])
    const isGenerating = ref(false)
    const generatedVideoUrl = ref('')

    const progressInfo = reactive({
      visible: false,
      percentage: 0,
      status: '',
      text: ''
    })

    // 计算属性
    const ffmpegStatus = computed(() => {
      if (isInitializing.value) {
        return { text: '初始化中...', class: 'status-loading' }
      }
      if (isFFmpegLoaded.value) {
        return { text: '已就绪', class: 'status-success' }
      }
      if (ffmpegError.value) {
        return { text: '初始化失败', class: 'status-error' }
      }
      return { text: '未初始化', class: 'status-default' }
    })

    const canGenerateVideo = computed(() => {
      return isFFmpegLoaded.value && selectedFiles.value.length >= 2 && !isGenerating.value
    })

    // 方法
    const updateEnvironmentInfo = () => {
      environmentInfo.isElectron = electronFileHandler.isElectron
      environmentInfo.sharedArrayBufferAvailable = typeof SharedArrayBuffer !== 'undefined'
      environmentInfo.webAssemblyAvailable = typeof WebAssembly !== 'undefined'
      
      if (environmentInfo.isElectron && window.electron) {
        environmentInfo.electronVersion = window.electron.versions?.electron || 'Unknown'
        environmentInfo.platform = window.electron.platform || 'Unknown'
      }
    }

    const initializeFFmpeg = async () => {
      isInitializing.value = true
      ffmpegError.value = ''
      
      try {
        const result = await ffmpegHelper.initialize((event) => {
          switch (event.type) {
            case 'status':
              progressInfo.visible = true
              progressInfo.text = event.message
              break
            case 'progress':
              progressInfo.percentage = event.progress
              break
          }
        })

        if (result.success) {
          isFFmpegLoaded.value = true
          ElMessage.success('FFmpeg 初始化成功')
        } else {
          throw new Error(result.error)
        }
      } catch (error) {
        ffmpegError.value = error.message
        ElMessage.error(`FFmpeg 初始化失败: ${error.message}`)
      } finally {
        isInitializing.value = false
        progressInfo.visible = false
      }
    }

    const selectFiles = async () => {
      try {
        const files = await electronFileHandler.selectFiles({
          multiple: true,
          filters: [
            { name: '图片文件', extensions: ['jpg', 'jpeg', 'png', 'gif'] }
          ]
        })
        
        selectedFiles.value = files
        ElMessage.success(`选择了 ${files.length} 个文件`)
      } catch (error) {
        ElMessage.error(`文件选择失败: ${error.message}`)
      }
    }

    const generateTestVideo = async () => {
      if (selectedFiles.value.length < 2) {
        ElMessage.warning('请至少选择两个图片文件')
        return
      }

      isGenerating.value = true
      progressInfo.visible = true
      progressInfo.percentage = 0
      progressInfo.text = '开始生成视频...'

      try {
        const result = await ffmpegHelper.generateTransitionVideo(
          selectedFiles.value[0],
          selectedFiles.value[1],
          {
            duration: 3,
            effect: 'fade',
            width: 640,
            height: 480
          }
        )

        generatedVideoUrl.value = result.videoUrl
        ElMessage.success('视频生成成功')
      } catch (error) {
        ElMessage.error(`视频生成失败: ${error.message}`)
      } finally {
        isGenerating.value = false
        progressInfo.visible = false
      }
    }

    const downloadVideo = () => {
      if (generatedVideoUrl.value) {
        const a = document.createElement('a')
        a.href = generatedVideoUrl.value
        a.download = 'generated-video.mp4'
        a.click()
      }
    }

    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    // 生命周期
    onMounted(() => {
      updateEnvironmentInfo()
    })

    return {
      environmentInfo,
      isInitializing,
      isFFmpegLoaded,
      ffmpegError,
      ffmpegStatus,
      selectedFiles,
      isGenerating,
      canGenerateVideo,
      generatedVideoUrl,
      progressInfo,
      initializeFFmpeg,
      selectFiles,
      generateTestVideo,
      downloadVideo,
      formatFileSize
    }
  }
}
</script>

<style scoped>
.electron-ffmpeg-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.environment-info,
.ffmpeg-status,
.file-info,
.progress-info,
.generated-video {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
}

.environment-info ul {
  list-style: none;
  padding: 0;
}

.environment-info li {
  margin: 5px 0;
}

.status-item {
  margin: 5px 0;
}

.status-success {
  color: #67c23a;
  font-weight: bold;
}

.status-error {
  color: #f56c6c;
  font-weight: bold;
}

.status-loading {
  color: #409eff;
  font-weight: bold;
}

.status-default {
  color: #909399;
}

.error-message {
  color: #f56c6c;
  margin-top: 10px;
  padding: 10px;
  background-color: #fef0f0;
  border-radius: 3px;
}

.actions {
  margin: 20px 0;
}

.actions .el-button {
  margin-right: 10px;
}

.file-item {
  margin: 5px 0;
  padding: 5px;
  background-color: #f5f5f5;
  border-radius: 3px;
}

.progress-text {
  margin-top: 10px;
  font-size: 14px;
  color: #666;
}

.video-actions {
  margin-top: 10px;
}
</style>
