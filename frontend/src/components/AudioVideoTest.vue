<template>
  <div class="audio-video-test">
    <h3>音频视频生成测试</h3>
    
    <!-- 环境信息 -->
    <div class="environment-section">
      <h4>环境信息</h4>
      <ul>
        <li>运行环境: {{ environmentInfo.isElectron ? 'Electron' : '浏览器' }}</li>
        <li>音频支持: {{ audioSupported ? '是' : '否' }}</li>
        <li>默认音频: {{ defaultAudioAvailable ? '可用' : '不可用' }}</li>
      </ul>
    </div>

    <!-- 文件选择 -->
    <div class="file-selection">
      <h4>选择图片文件</h4>
      <el-button type="primary" @click="selectImages" :disabled="isProcessing">
        选择图片 ({{ selectedImages.length }}/2)
      </el-button>
      
      <div v-if="selectedImages.length > 0" class="selected-files">
        <div v-for="(image, index) in selectedImages" :key="index" class="file-item">
          <span>{{ image.name }}</span>
          <span>({{ formatFileSize(image.size) }})</span>
        </div>
      </div>
    </div>

    <!-- 音频设置 -->
    <div class="audio-settings">
      <h4>音频设置</h4>
      
      <div class="setting-item">
        <el-switch v-model="audioEnabled" />
        <span>启用背景音乐</span>
      </div>
      
      <div v-if="audioEnabled" class="audio-options">
        <div class="setting-item">
          <el-radio-group v-model="audioSource">
            <el-radio value="default">默认音乐</el-radio>
            <el-radio value="custom">自定义音乐</el-radio>
          </el-radio-group>
        </div>
        
        <div v-if="audioSource === 'custom'" class="custom-audio">
          <el-button @click="selectAudioFile" :disabled="isProcessing">
            选择音频文件
          </el-button>
          <div v-if="customAudioFile" class="selected-audio">
            {{ customAudioFile.name }} ({{ formatFileSize(customAudioFile.size) }})
          </div>
        </div>
        
        <div class="setting-item">
          <span>音量: </span>
          <el-slider v-model="audioVolume" :min="0" :max="1" :step="0.1" style="width: 200px;" />
          <span>{{ Math.round(audioVolume * 100) }}%</span>
        </div>
      </div>
    </div>

    <!-- 视频设置 -->
    <div class="video-settings">
      <h4>视频设置</h4>
      
      <div class="setting-item">
        <span>时长: </span>
        <el-input-number v-model="videoDuration" :min="1" :max="30" />
        <span>秒</span>
      </div>
      
      <div class="setting-item">
        <span>过渡效果: </span>
        <el-select v-model="transitionEffect">
          <el-option label="淡入淡出" value="fade" />
          <el-option label="交叉溶解" value="crossfade" />
          <el-option label="滑动" value="slide" />
          <el-option label="缩放" value="zoom" />
        </el-select>
      </div>
    </div>

    <!-- 生成按钮 -->
    <div class="generate-section">
      <el-button 
        type="success" 
        size="large"
        @click="generateVideo"
        :disabled="!canGenerate"
        :loading="isProcessing"
      >
        {{ isProcessing ? '生成中...' : '生成视频' }}
      </el-button>
    </div>

    <!-- 进度显示 -->
    <div v-if="isProcessing" class="progress-section">
      <h4>处理进度</h4>
      <el-progress :percentage="progress" :status="progressStatus" />
      <div class="progress-text">{{ progressText }}</div>
    </div>

    <!-- 结果显示 -->
    <div v-if="generatedVideo" class="result-section">
      <h4>生成结果</h4>
      <video :src="generatedVideo.url" controls width="400" height="300" />
      <div class="video-info">
        <p>文件大小: {{ formatFileSize(generatedVideo.size) }}</p>
        <p>包含音频: {{ generatedVideo.hasAudio ? '是' : '否' }}</p>
      </div>
      <div class="video-actions">
        <el-button @click="downloadVideo">下载视频</el-button>
        <el-button @click="resetTest">重新测试</el-button>
      </div>
    </div>

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-section">
      <el-alert
        :title="errorMessage"
        type="error"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import ffmpegHelper from '@/utils/ffmpegHelper'
import electronFileHandler from '@/utils/electronFileHandler'

export default {
  name: 'AudioVideoTest',
  setup() {
    // 响应式数据
    const environmentInfo = reactive({
      isElectron: false,
      audioSupported: false
    })

    const selectedImages = ref([])
    const audioEnabled = ref(false)
    const audioSource = ref('default')
    const customAudioFile = ref(null)
    const audioVolume = ref(0.5)
    const videoDuration = ref(5)
    const transitionEffect = ref('crossfade')
    
    const isProcessing = ref(false)
    const progress = ref(0)
    const progressStatus = ref('')
    const progressText = ref('')
    const generatedVideo = ref(null)
    const errorMessage = ref('')
    
    const audioSupported = ref(false)
    const defaultAudioAvailable = ref(false)

    // 计算属性
    const canGenerate = computed(() => {
      return selectedImages.value.length >= 2 && !isProcessing.value
    })

    // 方法
    const updateEnvironmentInfo = () => {
      environmentInfo.isElectron = electronFileHandler.isElectron
      audioSupported.value = typeof Audio !== 'undefined'
      
      // 检查默认音频是否可用
      checkDefaultAudio()
    }

    const checkDefaultAudio = async () => {
      try {
        const audioModule = await import('@/assets/mp1.mp3')
        defaultAudioAvailable.value = !!audioModule.default
      } catch (error) {
        console.warn('默认音频不可用:', error)
        defaultAudioAvailable.value = false
      }
    }

    const selectImages = async () => {
      try {
        const files = await electronFileHandler.selectFiles({
          multiple: true,
          filters: [
            { name: '图片文件', extensions: ['jpg', 'jpeg', 'png', 'gif'] }
          ]
        })
        
        if (files.length > 2) {
          selectedImages.value = files.slice(0, 2)
          ElMessage.warning('只能选择2张图片，已自动选择前2张')
        } else {
          selectedImages.value = files
        }
        
        ElMessage.success(`选择了 ${selectedImages.value.length} 张图片`)
      } catch (error) {
        ElMessage.error(`图片选择失败: ${error.message}`)
      }
    }

    const selectAudioFile = async () => {
      try {
        const files = await electronFileHandler.selectFiles({
          multiple: false,
          filters: [
            { name: '音频文件', extensions: ['mp3', 'wav', 'aac', 'm4a'] }
          ]
        })
        
        if (files.length > 0) {
          customAudioFile.value = files[0]
          ElMessage.success('音频文件选择成功')
        }
      } catch (error) {
        ElMessage.error(`音频文件选择失败: ${error.message}`)
      }
    }

    const generateVideo = async () => {
      if (selectedImages.value.length < 2) {
        ElMessage.error('请选择至少2张图片')
        return
      }

      isProcessing.value = true
      progress.value = 0
      progressText.value = '开始处理...'
      errorMessage.value = ''
      generatedVideo.value = null

      try {
        // 初始化 FFmpeg
        if (!ffmpegHelper.isLoaded) {
          progressText.value = '初始化 FFmpeg...'
          await ffmpegHelper.initialize((event) => {
            if (event.type === 'status') {
              progressText.value = event.message
            } else if (event.type === 'progress') {
              progress.value = Math.min(event.progress * 0.3, 30)
            }
          })
        }

        // 生成视频
        progressText.value = '生成视频...'
        const result = await ffmpegHelper.generateTransitionVideo(
          selectedImages.value[0],
          selectedImages.value[1],
          {
            duration: videoDuration.value,
            effect: transitionEffect.value,
            width: 640,
            height: 480,
            enableAudio: audioEnabled.value,
            audioFile: audioSource.value === 'custom' ? customAudioFile.value : null,
            audioVolume: audioVolume.value
          }
        )

        generatedVideo.value = {
          url: result.videoUrl,
          size: result.size,
          hasAudio: result.hasAudio
        }

        progress.value = 100
        progressStatus.value = 'success'
        progressText.value = '视频生成完成！'
        ElMessage.success('视频生成成功')

      } catch (error) {
        console.error('视频生成失败:', error)
        errorMessage.value = error.message
        progressStatus.value = 'exception'
        ElMessage.error(`视频生成失败: ${error.message}`)
      } finally {
        isProcessing.value = false
      }
    }

    const downloadVideo = () => {
      if (generatedVideo.value) {
        const a = document.createElement('a')
        a.href = generatedVideo.value.url
        a.download = `video-${Date.now()}.mp4`
        a.click()
      }
    }

    const resetTest = () => {
      selectedImages.value = []
      customAudioFile.value = null
      generatedVideo.value = null
      errorMessage.value = ''
      progress.value = 0
      progressText.value = ''
    }

    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    // 生命周期
    onMounted(() => {
      updateEnvironmentInfo()
    })

    return {
      environmentInfo,
      selectedImages,
      audioEnabled,
      audioSource,
      customAudioFile,
      audioVolume,
      videoDuration,
      transitionEffect,
      isProcessing,
      progress,
      progressStatus,
      progressText,
      generatedVideo,
      errorMessage,
      audioSupported,
      defaultAudioAvailable,
      canGenerate,
      selectImages,
      selectAudioFile,
      generateVideo,
      downloadVideo,
      resetTest,
      formatFileSize
    }
  }
}
</script>

<style scoped>
.audio-video-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.environment-section,
.file-selection,
.audio-settings,
.video-settings,
.generate-section,
.progress-section,
.result-section,
.error-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
}

.setting-item {
  margin: 10px 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.audio-options {
  margin-left: 20px;
  padding-left: 15px;
  border-left: 2px solid #e0e0e0;
}

.selected-files,
.selected-audio {
  margin-top: 10px;
}

.file-item {
  padding: 5px;
  background-color: #f5f5f5;
  border-radius: 3px;
  margin: 5px 0;
}

.progress-text {
  margin-top: 10px;
  font-size: 14px;
  color: #666;
}

.video-info {
  margin: 10px 0;
}

.video-actions {
  margin-top: 10px;
}

.video-actions .el-button {
  margin-right: 10px;
}
</style>
