<template>
  <div class="ai-create-container">
    <!-- 顶部工具栏 -->
<!--    <div class="top-toolbar">-->
<!--      <a-space>-->
<!--        <a-select v-model:value="filterOptions.type" style="width: 120px">-->
<!--          <a-select-option value="all">类型：全部</a-select-option>-->
<!--          <a-select-option value="landscape">风景</a-select-option>-->
<!--          <a-select-option value="portrait">人像</a-select-option>-->
<!--          <a-select-option value="abstract">抽象</a-select-option>-->
<!--        </a-select>-->
<!--        <a-checkbox v-model:checked="filterOptions.groupSameInput">聚合相同输入</a-checkbox>-->
<!--        <a-checkbox v-model:checked="filterOptions.onlyFavorites">仅看收藏</a-checkbox>-->
<!--      </a-space>-->
<!--    </div>-->

    <div class="main-content">
      <!-- 左侧操作面板 -->
      <div class="control-panel">
        <!-- 标题栏 -->
        <a-tabs v-model:activeKey="activeTab">
          <a-tab-pane key="image" tab="图片生成"></a-tab-pane>
          <a-tab-pane key="video" tab="视频生成"></a-tab-pane>
        </a-tabs>

        <div v-show="activeTab==='image'">
          <!-- 描述输入框 -->
          <div class="control-section">
            <div class="section-title">描述想要生成的图片</div>
            <a-textarea
              v-model:value="description"
              :maxlength="maxLength"
              :auto-size="{ minRows: 4, maxRows: 6 }"
              placeholder="请输入详细描述..."
              class="description-input"
            />
            <div class="textarea-footer">
              <a-space>
                <a-button @click="selectPic">选择图片</a-button>
              </a-space>
              <p></p>
              <a-image
                :width="100"
                :src=picPath
              />
            </div>
          </div>

          <!-- 清晰度选择 -->
          <!--        <div class="control-section">-->
          <!--          <div class="section-title">选择清晰度</div>-->
          <!--          <a-radio-group v-model:value="resolution" button-style="solid">-->
          <!--            <a-radio-button value="1k">标清 1K</a-radio-button>-->
          <!--            <a-radio-button value="2k">高清 2K</a-radio-button>-->
          <!--          </a-radio-group>-->
          <!--        </div>-->

          <!-- 比例选择 -->
          <!--        <div class="control-section">-->
          <!--          <div class="section-title">比例</div>-->
          <!--          <div class="aspect-ratio-container">-->
          <!--            <div-->
          <!--              v-for="ratio in aspectRatios"-->
          <!--              :key="ratio.value"-->
          <!--              class="aspect-ratio-item"-->
          <!--              :class="{ active: aspectRatio === ratio.value }"-->
          <!--              @click="aspectRatio = ratio.value"-->
          <!--            >-->
          <!--              <div-->
          <!--                class="aspect-ratio-box"-->
          <!--                :style="{-->
          <!--                  width: `${ratio.width * 8}px`,-->
          <!--                  height: `${ratio.height * 8}px`-->
          <!--                }"-->
          <!--              ></div>-->
          <!--              <div class="aspect-ratio-text">{{ ratio.value }}</div>-->
          <!--            </div>-->
          <!--          </div>-->
          <!--        </div>-->

          <!-- 图片尺寸 -->
          <div class="control-section">
            <div class="section-title">
              图片尺寸
              <a-tooltip title="图片尺寸会影响生成速度和质量">
                <question-circle-outlined />
              </a-tooltip>
            </div>
            <div class="image-size-container">
              <div class="size-input-group">
                <span class="size-label">W</span>
                <a-input-number v-model:value="imageSize.width" :min="256" :max="2048" />
              </div>
              <a-button class="swap-button" @click="swapDimensions">
                <swap-outlined />
              </a-button>
              <div class="size-input-group">
                <span class="size-label">H</span>
                <a-input-number v-model:value="imageSize.height" :min="256" :max="2048" />
              </div>
            </div>
          </div>

            <!-- 生成按钮 -->
            <a-button
              type="primary"
              class="generate-button"
              size="large"
              :loading="isGenerating"
              @click="generateImage"
            >
              立即生成
            </a-button>
        </div>
        <div v-show="activeTab==='video'">
          <!-- 主要内容区域 -->
          <div class="content-container">
            <!-- 图片上传区域 -->
            <div class="upload-section">
              <a-upload-dragger
                v-model:fileList="fileList"
                name="file"
                :multiple="false"
                accept="image/*"
                :customRequest="customRequest"
                @change="handleUpload"
                class="upload-dragger"
              >
                <div class="upload-content">
                  <p class="upload-icon">
                    <picture-outlined />
                  </p>
                  <p class="upload-text">上传图片</p>
                </div>
              </a-upload-dragger>

              <div class="upload-description">
                <p>结合图片，描述你想生成的画面和动作。</p>
                <p>例如：海浪拍打着沙滩，黄色的月亮在天空中缓缓升起。</p>
              </div>
            </div>

            <!-- 基础设置面板 -->
            <div class="settings-panel">
              <div class="setting-section">
                <div class="setting-title">
                  <span>生成时长</span>
                </div>
                <a-radio-group
                  v-model:value="formData.duration"
                  button-style="solid"
                  class="duration-selector"
                >
                  <a-radio-button value="5s">5s</a-radio-button>
                  <a-radio-button value="10s">10s</a-radio-button>
                </a-radio-group>
              </div>
            </div>

            <!-- 底部操作区域 -->
            <div class="action-section">
              <a-button
                type="primary"
                size="large"
                :loading="isGenerating"
                @click="generateVideo"
                class="generate-button"
              >
                <template #icon>
                  <play-circle-outlined />
                </template>
                生成视频
              </a-button>

              <div class="cost-info">
                <span class="cost-text">消耗积分: 10</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧内容展示区域 -->
      <div class="content-display">
        <template v-if="generatedImage">
          <div class="image-container">
            <a-image :src="generatedImage" :width="imageSize.width" :height="imageSize.height" />
            <div class="image-actions">
              <a-button type="primary">
                <template #icon><download-outlined /></template>
                下载
              </a-button>
              <a-button>
                <template #icon><heart-outlined /></template>
                收藏
              </a-button>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="empty-state">
            <picture-outlined class="empty-icon" />
            <p class="empty-text">生成的结果将在这里呈现</p>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ipcApiRoute } from '@/api';
import { ipc } from '@/utils/ipcRenderer';
import { ref, computed, reactive } from 'vue';
import {
  PictureOutlined, PlayCircleOutlined,
  QuestionCircleOutlined,
  SwapOutlined,
  UploadOutlined
} from '@ant-design/icons-vue';
import axios from 'axios';
import storage from 'store2';
import { sign,getDateTimeNow } from '@/utils/signutils';
import CryptoJS from "crypto-js";
import url from "url";
import util from "util";
import { doVolcCVRequest } from '@/utils/volcengine-sign';

const response = ref(null);

async function submitImage() {

  try {
    const result = await doVolcCVRequest(picPath.value);
    response.value = result;
    console.log("result", result)
  } catch (err) {
    alert("请求失败，请查看控制台日志");
    console.error(err);
  }
}

const generatedImageUrl = ref("https://visual.volcengineapi.com?Action=CVProcess&Version=2022-08-31");

// 标签页状态
const activeTab = ref('image');
const fileList  = ref([]);
const picPath  = ref("");

// 描述输入框
const description = ref('请在当前四维彩超图像基础上，清晰显示出胎儿完整的四肢形态，包括两只手和两只脚，手的五指张开或自然弯曲，脚的脚趾也能清楚分辨，让胎儿的肢体结构完整且直观地呈现出来。');
const maxLength = 800;
const descriptionCount = computed(() => `${description.value.length}/${maxLength}`);

// 清晰度选择
const resolution = ref('1k');

// 比例选择
const aspectRatio = ref('1:1');
const aspectRatios = [
  { value: '21:9', width: 21, height: 9 },
  { value: '16:9', width: 16, height: 9 },
  { value: '3:2', width: 3, height: 2 },
  { value: '4:3', width: 4, height: 3 },
  { value: '1:1', width: 1, height: 1 },
  { value: '3:4', width: 3, height: 4 },
  { value: '2:3', width: 2, height: 3 },
  { value: '9:16', width: 9, height: 16 }
];

// 图片尺寸
const imageSize = reactive({
  width: 1328,
  height: 1328
});


// 交换宽高
const swapDimensions = () => {
  const temp = imageSize.width;
  imageSize.width = imageSize.height;
  imageSize.height = temp;
};

// 顶部工具栏状态
const filterOptions = reactive({
  onlyFavorites: false,
  groupSameInput: false,
  type: 'all'
});

// 生成图片
const isGenerating = ref(false);
const generatedImage = ref('');

const generateImage = () => {
  isGenerating.value = true;
  // submitImage();
  backendRequest('img');
  // 模拟生成过程
  // setTimeout(() => {
  //   // 实际项目中这里应该调用API生成图片
  //   generatedImage.value = 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png';
  //   isGenerating.value = false;
  // }, 2000);
};

function selectPic() {
  ipc.invoke(ipcApiRoute.os.selectPic).then(r => {
    picPath.value = r;
    console.log("picPath.value", picPath.value)
  })
}

const downPic = () => {
  let base64 = picPath.value.replace(/^data:image\/\w+;base64,/, "");
  let dataBuffer = new Buffer.from(base64, 'base64');
  // console.log(dataBuffer)
  ipc.invoke(ipcApiRoute.os.writeFile,dataBuffer).then(r => {
    console.log(r)
  })
}

// 导入图片
const importImage = (data) => {
  // 在实际项目中，这里应该调用Electron的API来选择本地图片
  console.log('导入图片',data);
};

// 上传图片

// 自定义上传请求
const customRequest = (options) => {
  const { file, onSuccess, onError } = options;

  // 模拟上传过程
  setTimeout(() => {
    if (Math.random() > 0.1) {
      onSuccess();
      message.success('图片上传成功');
    } else {
      onError();
      message.error('上传失败，请重试');
    }
  }, 1000);
};


// 文件上传处理
const handleUpload = (info) => {
  const { status } = info.file;
  if (status !== 'uploading') {
    console.log(info.file, info.fileList);
  }
  if (status === 'done') {
    message.success(`${info.file.name} 文件上传成功`);
  } else if (status === 'error') {
    message.error(`${info.file.name} 文件上传失败`);
  }
};

const formData = reactive({
  description: '',
  duration: '5s',
  aspectRatio: 'auto'
});
// 生成状态
// const isGenerating = ref(false);
// 生成视频
const generateVideo = () => {
  if (fileList.value.length === 0) {
    message.warning('请先上传图片');
    return;
  }

  isGenerating.value = true;

  // 模拟生成过程
  setTimeout(() => {
    isGenerating.value = false;
    message.success('视频生成完成');
  }, 3000);
};

function getBodySha(body) {
  // const hash = crypto.createHash('sha256');
  // if (typeof body === 'string') {
  //   hash.update(body);
  // } else if (body instanceof url.URLSearchParams) {
  //   hash.update(body.toString());
  // } else if (util.isBuffer(body)) {
  //   hash.update(body);
  // }
  // return hash.digest('hex');
  if(typeof body === 'string'){
    return CryptoJS.SHA256(body).toString();
  }else {
    return CryptoJS.SHA256(body.toString()).toString();
  }
}

/**
 * Send back-end requests
 */
async function backendRequest(kind){

  const body = {
    req_key: "high_aes_scheduler_svr_controlnet_v2.0",
    "binary_data_base64": picPath.value,
    "prompt": "请在当前四维彩超图像基础上，清晰显示出胎儿完整的四肢形态，包括两只手和两只脚，手的五指张开或自然弯曲，脚的脚趾也能清楚分辨，让胎儿的肢体结构完整且直观地呈现出来。",
    "controlnet_args":[{
      type:"pose",
      strength:0.6,
      binary_data_index:0
    }]
  }

  const params = {
    headers: {
      "X-Date": getDateTimeNow(),
      "Host": "visual.volcengineapi.com"
    },
    method: 'POST',
    query: {
      // Version: '2022-08-31',
      // Action: 'CVProcess',
      Version: '2024-06-06',
      Action: 'HighAESSchedulerSVRControlnetV20',
    },
    accessKeyId: 'AKLTNmMwMTYxNTVjMWMwNDA2NWE0MmIwMTczMTY2ZGI4OWU',
    secretAccessKey: 'TW1GaE56UXdOREUzT0dWak5HTTNNemsxTkdObE1tVTFPR1F4WXpReFpESQ==',
    serviceName: 'cv',
    region: 'cn-north-1'
  };

  const queryList= ""

  ipc.invoke(ipcApiRoute.effect.doRequest,{"body":body,"queryList":queryList}).then(r => {
    console.log(r)
  })

  // console.log(import.meta.env.VOLC_ACCESSKEY)
  // console.log(import.meta.env.VOLC_SECRETKEY)
  headers['Authorization'] = await sign(params);

  for (const key in params.query) {
    if (params.query[key] === undefined || params.query[key] === null) {
      params.query[key] = '';
    }
  }

  if(kind==="img"){
    // console.log('GO_URL:', import.meta.env.VITE_IMG_URL);
    // const cfg = {
    //   baseURL: import.meta.env.VITE_IMG_URL,
    //   method: 'POST',
    //   url: '',
    //   timeout: 3000,
    //   headers: params.headers,
    //   data: {
    //     req_key: "high_aes_scheduler_svr_controlnet_v2.0",
    //     binary_data_base64: picPath.value,
    //     prompt: description.value,
    //     controlnet_args: [{
    //       "type": "pose",
    //       "strength": 0.6,
    //       "binary_data_index": 0
    //     }],
    //   }
    // }
    // axios(cfg).then(res => {
    //   console.log('res:', res);
    //   const data = res.data || null;
    //   message.info(`go服务返回: ${data}`, );
    // })
  }else if(kind==="video"){

  }
  isGenerating.value = false;
}

</script>
<style scoped lang="less">
.ai-create-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f0f2f5;
  color: rgba(0, 0, 0, 0.88);
}

.top-toolbar {
  display: flex;
  justify-content: flex-end;
  padding: 12px 16px;
  background-color: #ffffff;
  border-bottom: 1px solid #d9d9d9;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.control-panel {
  width: 30%;
  padding: 16px;
  background-color: #ffffff;
  border-right: 1px solid #d9d9d9;
  overflow-y: auto;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

.content-display {
  width: 70%;
  background-color: #fafafa;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: auto;
}

.control-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 14px;
  margin-bottom: 8px;
  color: rgba(0, 0, 0, 0.88);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

.description-input {
  background-color: #ffffff;
  border-color: #d9d9d9;
  color: rgba(0, 0, 0, 0.88);

  &:focus {
    border-color: #52c41a;
    box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
  }
}

.textarea-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.char-count {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
}

.aspect-ratio-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.aspect-ratio-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.aspect-ratio-box {
  background-color: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  transition: all 0.3s;

  &:hover {
    border-color: #52c41a;
    box-shadow: 0 2px 4px rgba(82, 196, 26, 0.2);
  }
}

.aspect-ratio-text {
  margin-top: 4px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
}

.aspect-ratio-item.active .aspect-ratio-box {
  border-color: #52c41a;
  background-color: #f6ffed;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
}

.aspect-ratio-item.active .aspect-ratio-text {
  color: #52c41a;
  font-weight: 500;
}

.image-size-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.size-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.size-label {
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-weight: 500;
}

.swap-button {
  display: flex;
  align-items: center;
  justify-content: center;
  border-color: #d9d9d9;

  &:hover {
    border-color: #52c41a;
    color: #52c41a;
  }
}

.generate-button {
  width: 100%;
  margin-top: 16px;
  background-color: #52c41a;
  border-color: #52c41a;

  &:hover {
    background-color: #73d13d;
    border-color: #73d13d;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: rgba(0, 0, 0, 0.45);
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  color: rgba(0, 0, 0, 0.25);
}

.empty-text {
  font-size: 16px;
}

.image-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.image-actions {
  display: flex;
  gap: 12px;
}

:deep(.ant-tabs-tab) {
  color: rgba(0, 0, 0, 0.65);
}

:deep(.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn) {
  color: #52c41a;
  font-weight: 500;
}

:deep(.ant-tabs-ink-bar) {
  background-color: #52c41a;
}

:deep(.ant-input),
:deep(.ant-input-number),
:deep(.ant-select-selector) {
  background-color: #ffffff !important;
  border-color: #d9d9d9 !important;
  color: rgba(0, 0, 0, 0.88) !important;

  &:focus {
    border-color: #52c41a !important;
    box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2) !important;
  }
}

:deep(.ant-input-number-handler-wrap) {
  background-color: #ffffff !important;
  border-left-color: #d9d9d9 !important;
}

:deep(.ant-checkbox-wrapper),
:deep(.ant-radio-wrapper) {
  color: rgba(0, 0, 0, 0.88);
}

:deep(.ant-radio-button-wrapper) {
  background-color: #ffffff;
  border-color: #d9d9d9;
  color: rgba(0, 0, 0, 0.88);

  &:hover {
    border-color: #52c41a;
    color: #52c41a;
  }

  &.ant-radio-button-wrapper-checked {
    background-color: #52c41a;
    border-color: #52c41a;
    color: #ffffff;
  }
}

:deep(.ant-select-dropdown) {
  background-color: #ffffff;
  border: 1px solid #d9d9d9;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:deep(.ant-select-item) {
  color: rgba(0, 0, 0, 0.88);

  &:hover {
    background-color: #f6ffed;
  }
}

:deep(.ant-select-item-option-selected:not(.ant-select-item-option-disabled)) {
  background-color: #f6ffed;
  color: #52c41a;
  font-weight: 500;
}
</style>
