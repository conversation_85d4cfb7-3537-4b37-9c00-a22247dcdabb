<template>
  <div class="app-container">


    <div class="ai-create-container">
      <div class="main-content">
        <!-- 左侧操作面板 -->
        <div class="control-panel">
          <!-- 标题栏 -->
          <el-tabs v-model="activeTab">
            <el-tab-pane label="局部图片生成" name="image"></el-tab-pane>
<!--            <el-tab-pane label="视频生成" name="video"></el-tab-pane>-->
          </el-tabs>

          <div v-show="activeTab==='image'">
            <!-- 描述输入框 -->
            <div class="control-section">
              <div class="section-title">描述想要生成的图片</div>
              <el-input
                  v-model="description"
                  type="textarea"
                  :maxlength="maxLength"
                  :autosize="{ minRows: 4, maxRows: 6 }"
                  placeholder="请输入详细描述..."
                  class="description-input"
                  show-word-limit
              />
              <div class="textarea-footer">
                <div>
                  <el-space>
                    <el-button @click="selectPic">选择图片</el-button>
                    <!--                  <file-upload v-model="form.urlImg" :file-type="['png','jpg']" :file-size="4.7"/>-->

                  </el-space>
                </div>
                <div>
                  <el-image
                    v-show="form.urlImg"
                    :width="100"
                    :src="form.urlImg"
                  />
                </div>
              </div>
              <p style="font-size: 12px;"><span style="color: red;">注意：</span>图片尺寸小于4096*4096，长边与短边比例在3以内，超出此比例或比例相对极端，会导致报错</p>
            </div>

            <!-- 生成按钮 -->
            <el-button
                type="primary"
                class="generate-button"
                size="large"
                :loading="isGenerating"
                @click="genImage(1)"
            >
              立即生成
            </el-button>
          </div>
        </div>

        <!-- 右侧内容展示区域 -->
        <div class="content-display">
          <template v-if="generatedImage">
            <div class="image-container">
              <el-image :src="generatedImage" :width="imageSize.width" :height="imageSize.height" />
              <div class="image-actions">
                <el-button type="primary" @click="handleDownloadQrIMg">
                  <el-icon></el-icon>
                  下载
                </el-button>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="empty-state">
              <el-icon class="empty-icon"><Picture /></el-icon>
              <p class="empty-text">生成的结果将在这里呈现</p>
            </div>
          </template>
        </div>
      </div>
    </div>

    <div class="reference-tags" >
      <el-card>
        <span class="text-info" >参考提示词</span>
        <div class="tag-container">
          <el-tag class="tag-info" v-for="tag in tags" :key="tag">{{ tag }}</el-tag>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup name="GenimgautoSeeEdit">

import {message} from "ant-design-vue";
import axios from "axios";
import {ipc} from "@/utils/ipcRenderer";
import {ipcApiRoute} from "@/api";
import { ref,toRefs, reactive, getCurrentInstance, computed, watch, onMounted, onUnmounted } from 'vue'

const { proxy } = getCurrentInstance();

const activeTab = ref('image');
const genimgautoList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const serverUrl = ref("");
const data = reactive({
  form: {

  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: null,
    pormat: null,
    urlImg: null,
    baser64String: null,
    genType: null
  },
  rules: {
  }
});

const { queryParams, form, rules } = toRefs(data);


// 描述输入框
const description = ref('在当前四维彩超图像基础上，中国婴儿，皮肤细腻光滑，水润，有光泽，白皙透红，脸型圆润饱满，五官小巧精致，眼睛闭着，可爱，白胖水嫩，人体结构清晰');

const tags = ref([
  "皮肤细腻光滑",
  "脸颊圆润饱满",
  "五官小巧精致",
  "一个月大",
  "眼睛紧闭",
  "睫毛纤长卷翘",
  "表情平静",
  "侧卧",
  "睫毛纤长卷翘",
  "双目轻阖",
  "脑袋微微偏向一侧",
  "圆润饱满的脸颊","肉嘟嘟的质感","稀疏柔软的胎发","注意胎儿角度",
  "可爱","嫩白胖","细致的脸"
])

function selectPic() {
  ipc.invoke(ipcApiRoute.os.selectPic).then(r => {
    // console.log( r)
    form.value.urlImg = r;
    console.log("form.urlImg", form.value.urlImg)
  })
}

function getUrl() {
  ipc.invoke(ipcApiRoute.cross.getCrossUrl, {name: 'javaapp'}).then(url => {
    serverUrl.value = url;
    // message.info(`服务地址: ${url}`);
  })
}

const genImage = (type) => {
  getUrl();
  if (type == 1 && serverUrl.value == "") {
    message.info("请先获取服务地址");
    return
  }
  isGenerating.value = true;
  const imgUrl = form.value.urlImg
  const genimgauto = {
    pormat: description.value,
    baser64String: imgUrl
  }
  if (type == 1) {
    const testApi = serverUrl.value + '/genSeedEditImg';
    // const testApi = "http://127.0.0.1:8081" + '/genSeedEditImg';
    const cfg = {
      method: 'POST',
      url: testApi,
      data: genimgauto,
      timeout: 600000,
    }
    axios(cfg).then(res => {
      console.log('res:', res);
      const data = res.data || null;
      // message.info(`服务返回: ${data}`);
      console.log('data:', data)
      if(data!=null){
        form.value.binaryDataBase64 = data.data[0];
        isGenerating.value = false;
        handleDownloadQrIMg(form.value.binaryDataBase64);
      }

    })
    // handleDownloadQrIMg(form.value.binaryDataBase64);
  } else {
    ipc.invoke(ipcApiRoute.cross.requestApi, {name: 'javaapp', urlPath: '/genSeedEditImg', params: { id: ''}}).then(res => {
      console.log('res:', res);
      const data = res || null;
      message.info(`服务返回: ${data}`);
    })
  }
};

const downPic = () => {
  let base64 = form.value.binaryDataBase64.replace(/^data:image\/\w+;base64,/, "");
  let dataBuffer = new Buffer.from(base64, 'base64');
  // console.log(dataBuffer)
}

//qrBase64是后台传回来的base64数据
const handleDownloadQrIMg = () =>  {
  // const qrBase64 = "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"
  // 这里是获取到的图片base64编码,这里只是个例子哈，要自行编码图片替换这里才能测试看到效果
  console.log(form.value.binaryDataBase64)
  const imgUrl = `data:image/png;base64,${form.value.binaryDataBase64}`
  // const imgUrl = `data:image/png;base64,${qrBase64}`
  generatedImage.value= imgUrl;
  // 如果浏览器支持msSaveOrOpenBlob方法（也就是使用IE浏览器的时候），那么调用该方法去下载图片
  if (window.navigator.msSaveOrOpenBlob) {
    const bstr = atob(imgUrl.split(',')[1])
    let n = bstr.length
    const u8arr = new Uint8Array(n)
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n)
    }
    const blob = new Blob([u8arr])
    window.navigator.msSaveOrOpenBlob(blob, Math.floor(Date.now() / 1000) + '.' + 'png')
  } else {
    // 这里就按照chrome等新版浏览器来处理
    const a = document.createElement('a')
    a.href = imgUrl
    a.setAttribute('download', 'baby-pic.png')
    a.click()
  }
}
const maxLength = 800;

// 图片尺寸
const imageSize = reactive({
  width: 1328,
  height: 1328
});

// 生成图片
const isGenerating = ref(false);
const generatedImage = ref('');

getUrl();
</script>
<style scoped lang="scss">
.ai-create-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f7fa;
  color: #303133;
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.control-panel {
  width: 30%;
  padding: 16px;
  background-color: #ffffff;
  border-right: 1px solid #dcdfe6;
  overflow-y: auto;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

.content-display {
  width: 70%;
  background-color: #f5f7fa;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: auto;
}

.control-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 14px;
  margin-bottom: 8px;
  color: #303133;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

.description-input {
  background-color: #ffffff;
  border-color: #dcdfe6;
  color: #303133;
}

.textarea-footer {
  display: flex;
  margin-top: 8px;
  flex-wrap: wrap;
  align-content: space-around;
}

.textarea-footer div{
  margin-top: 8px;
}

.image-size-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.size-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.size-label {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

.swap-button {
  display: flex;
  align-items: center;
  justify-content: center;
  border-color: #dcdfe6;
}

.generate-button {
  width: 100%;
  margin-top: 16px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  color: #c0c4cc;
}

.empty-text {
  font-size: 16px;
}

.image-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.image-actions {
  display: flex;
  gap: 12px;
}

.upload-section {
  margin-bottom: 20px;
}

.upload-icon {
  font-size: 48px;
  color: #409eff;
}

.upload-description {
  margin-top: 16px;
  color: #606266;
  font-size: 14px;
}

.settings-panel {
  margin-bottom: 20px;
}

.setting-section {
  margin-bottom: 16px;
}

.setting-title {
  font-size: 14px;
  margin-bottom: 8px;
  color: #303133;
  font-weight: 500;
}

.duration-selector {
  width: 100%;
}

.action-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.cost-info {
  text-align: center;
  color: #606266;
  font-size: 14px;
}
.reference-tags {
  margin-top: 20px;
  .text-info{
    color: #606266;
    font-size: 20px;
    margin: 10px;


  }
  .tag-container{
    margin: 10px;
    .tag-info{
      margin: 5px;
    }
  }
}
</style>
