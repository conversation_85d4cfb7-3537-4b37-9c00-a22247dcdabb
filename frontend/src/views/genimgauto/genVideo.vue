<template>
  <div class="app-container">
    <div class="ai-create-container">
      <div class="main-content">
        <!-- 左侧操作面板 -->
        <div class="control-panel">
          <!-- 标题栏 -->
          <div class="panel-header">
            <h3 class="panel-title">视频生成</h3>
          </div>

          <!-- 图片上传区域 -->
          <div class="control-section">
            <div class="section-title">图像上传</div>
            <div class="upload-area">
              <div class="upload-grid">
                <!-- 首帧图片上传 -->
                <div class="upload-item">
                  <div
                    class="upload-placeholder"
                    :class="{ 'has-image': form.startImg }"
                    @click="handleUploadClick('start')"
                  >
                    <template v-if="form.startImg">
                      <img
                        :src="getImagePreview(form.startImg)"
                        class="preview-image"
                        alt="首帧预览"
                        @loadstart="handleImageLoadStart"
                        @load="(e) => handleImageLoad(e, 'upload')"
                      />
                      <div class="image-overlay">
                        <el-button size="small" type="primary" @click.stop="handleUploadClick('start')">
                          <el-icon><Edit /></el-icon>
                          更换
                        </el-button>
                        <el-button size="small" type="danger" @click.stop="removeImage('start')">
                          <el-icon><Delete /></el-icon>
                          删除
                        </el-button>
                      </div>
                    </template>
                    <template v-else>
                      <el-icon class="upload-icon"><Plus /></el-icon>
                      <div class="upload-text">上传首帧图像</div>
                      <div class="upload-subtext">过渡起始画面</div>
                    </template>
                  </div>
                </div>

                <!-- 尾帧图片上传 -->
                <div class="upload-item">
                  <div
                    class="upload-placeholder"
                    :class="{ 'has-image': form.endImg }"
                    @click="handleUploadClick('end')"
                  >
                    <template v-if="form.endImg">
                      <img
                        :src="getImagePreview(form.endImg)"
                        class="preview-image"
                        alt="尾帧预览"
                        @loadstart="handleImageLoadStart"
                        @load="(e) => handleImageLoad(e, 'upload')"
                      />
                      <div class="image-overlay">
                        <el-button size="small" type="primary" @click.stop="handleUploadClick('end')">
                          <el-icon><Edit /></el-icon>
                          更换
                        </el-button>
                        <el-button size="small" type="danger" @click.stop="removeImage('end')">
                          <el-icon><Delete /></el-icon>
                          删除
                        </el-button>
                      </div>
                    </template>
                    <template v-else>
                      <el-icon class="upload-icon"><Plus /></el-icon>
                      <div class="upload-text">上传尾帧图像</div>
                      <div class="upload-subtext">过渡结束画面</div>
                    </template>
                  </div>
                </div>
              </div>


            </div>



            <!-- 图片尺寸信息 -->
            <div v-if="imageInfo.startImg.loaded || imageInfo.endImg.loaded" class="image-info-section">
              <div class="section-title">图片尺寸信息</div>
              <div class="image-info-grid">
                <div v-if="imageInfo.startImg.loaded" class="image-info-item">
                  <div class="info-label">首帧图片</div>
                  <div class="info-content">
                    <span class="size-text">{{ imageInfo.startImg.width }} × {{ imageInfo.startImg.height }}</span>
                    <el-tag size="small" :type="getOrientationTagType(imageInfo.startImg.orientation)">
                      {{ getOrientationText(imageInfo.startImg.orientation) }}
                    </el-tag>
                    <span class="ratio-text">{{ imageInfo.startImg.aspectRatio.toFixed(2) }}:1</span>
                  </div>
                </div>
                <div v-if="imageInfo.endImg.loaded" class="image-info-item">
                  <div class="info-label">尾帧图片</div>
                  <div class="info-content">
                    <span class="size-text">{{ imageInfo.endImg.width }} × {{ imageInfo.endImg.height }}</span>
                    <el-tag size="small" :type="getOrientationTagType(imageInfo.endImg.orientation)">
                      {{ getOrientationText(imageInfo.endImg.orientation) }}
                    </el-tag>
                    <span class="ratio-text">{{ imageInfo.endImg.aspectRatio.toFixed(2) }}:1</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 智能视频尺寸设置 -->
            <div v-if="videoSize.isAutoCalculated" class="video-size-section">
              <div class="section-title">
                智能推荐视频尺寸
                <el-tooltip content="根据上传图片自动计算的最佳视频分辨率">
                  <el-icon class="help-icon"><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
              <div class="video-size-info">
                <div class="current-size">
                  <span class="size-label">当前尺寸:</span>
                  <span class="size-value">{{ videoSize.width }} × {{ videoSize.height }}</span>
                  <el-tag size="small" type="success">{{ videoSize.aspectRatio }}</el-tag>
                </div>
                <div v-if="videoSize.recommendedSizes.length > 1" class="size-options">
                  <span class="options-label">其他推荐:</span>
                  <div class="size-buttons">
                    <el-button
                      v-for="(size, index) in videoSize.recommendedSizes.slice(1, 4)"
                      :key="index"
                      size="small"
                      plain
                      class="size-option-btn"
                      @click="selectVideoSize(size)"
                    >
                      {{ size.width }}×{{ size.height }} ({{ size.name }})
                    </el-button>
                    <el-button
                      size="small"
                      plain
                      type="info"
                      class="reset-size-btn"
                      @click="resetVideoSize"
                    >
                      重置为默认
                    </el-button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 上传提示 -->
            <div class="upload-tips">
              <el-alert
                title="上传提示"
                type="info"
                :closable="false"
                show-icon
              >
                <template #default>
                  <p>• 支持 JPG、PNG、GIF 格式，文件大小不超过 10MB</p>
                  <p>• 建议图片尺寸比例一致，以获得更好的过渡效果</p>
                  <p>• 首帧图片将逐渐淡出，尾帧图片将逐渐淡入</p>
                  <p v-if="videoSize.isAutoCalculated">• 系统已根据图片尺寸智能推荐视频分辨率</p>
                </template>
              </el-alert>
            </div>
          </div>

          <!-- 视频时长设置 -->
          <div class="control-section">
            <div class="section-title">
              视频时长
              <el-tooltip content="选择过渡视频的播放时长">
                <el-icon class="help-icon"><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
            <el-radio-group v-model="videoDuration" class="duration-selector">
              <el-radio-button value="3">3秒</el-radio-button>
              <el-radio-button value="5">5秒</el-radio-button>
              <el-radio-button value="10">10秒</el-radio-button>
            </el-radio-group>
          </div>

          <!-- 过渡效果设置 -->
          <div class="control-section">
            <div class="section-title">
              过渡效果
              <el-tooltip content="选择图片之间的过渡动画效果">
                <el-icon class="help-icon"><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
            <el-select v-model="transitionEffect" placeholder="选择过渡效果" class="effect-selector">
              <el-option label="交叉溶解" value="crossfade" />
              <el-option label="淡入淡出" value="fade" />
              <el-option label="滑动过渡" value="slide" />
              <el-option label="缩放过渡" value="zoom" />
            </el-select>
          </div>

          <!-- 创意描述 -->
<!--          <div class="control-section">-->
<!--            <div class="section-title">创意描述（选填）</div>-->
<!--            <el-input-->
<!--              v-model="description"-->
<!--              type="textarea"-->
<!--              :maxlength="maxLength"-->
<!--              :autosize="{ minRows: 3, maxRows: 5 }"-->
<!--              placeholder="描述过渡效果的细节，如光影变化、色彩渐变等"-->
<!--              class="description-input"-->
<!--              show-word-limit-->
<!--            />-->
<!--          </div>-->

          <!-- 处理方式选择 -->
          <div class="control-section">
<!--            <div class="section-title">-->
<!--              处理方式-->
<!--              <el-tooltip content="本地处理速度更快且保护隐私，服务器处理功能更强大">-->
<!--                <el-icon class="help-icon"><QuestionFilled /></el-icon>-->
<!--              </el-tooltip>-->
<!--            </div>-->
<!--            <el-radio-group v-model="processingMethod" class="processing-method-selector">-->
<!--              <el-radio-button value="local" :disabled="!isFFmpegLoaded && !isFFmpegLoading">-->
<!--                本地处理-->
<!--                <span v-if="isFFmpegLoading" class="loading-text">加载中...</span>-->
<!--                <span v-else-if="!isFFmpegLoaded && ffmpegError" class="error-text">不可用</span>-->
<!--              </el-radio-button>-->
<!--              <el-radio-button value="api">-->
<!--                服务器处理-->
<!--              </el-radio-button>-->
<!--            </el-radio-group>-->

            <!-- FFmpeg状态提示 -->
            <div class="ffmpeg-status">
              <el-alert
                v-if="isFFmpegLoading"
                title="正在加载本地处理引擎..."
                type="info"
                :closable="false"
                show-icon
              />
              <el-alert
                v-else-if="ffmpegError"
                :title="`加载失败: ${ffmpegError}`"
                type="error"
                :closable="false"
                show-icon
              >
                <template #default>
                  <p>{{ ffmpegError }}</p>
                  <div style="margin-top: 8px;">
                    <el-button size="small" type="primary" @click="runDiagnostics">
                      运行诊断检查
                    </el-button>
                    <el-button size="small" @click="initializeFFmpeg">
                      重试初始化
                    </el-button>
                  </div>
                </template>
              </el-alert>
              <el-alert
                v-else-if="isFFmpegLoaded"
                title="本地处理引擎已就绪"
                type="success"
                :closable="false"
                show-icon
              />
            </div>
          </div>

          <!-- 灵感模式 -->
<!--          <div class="control-section">-->
<!--            <div class="section-title">-->
<!--              灵感模式-->
<!--              <el-tooltip content="开启后将增强创意效果，但可能与原图差异较大">-->
<!--                <el-icon class="help-icon"><QuestionFilled /></el-icon>-->
<!--              </el-tooltip>-->
<!--            </div>-->
<!--            <el-switch v-model="inspirationMode" />-->
<!--          </div>-->

          <!-- 背景音乐设置 -->
          <div class="control-section">
            <div class="section-title">
              背景音乐
              <el-tooltip content="为生成的视频添加背景音乐，音频长度会自动调整为视频长度">
                <el-icon class="help-icon"><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
            <div class="audio-controls">
              <div class="audio-enable">
                <el-switch
                  v-model="audioSettings.enabled"
                  @change="onAudioEnabledChange"
                />
                <span class="audio-label">启用背景音乐</span>
              </div>

              <div v-if="audioSettings.enabled" class="audio-options">
                <!-- 音频文件选择 -->
                <div class="audio-file-section">
                  <div class="audio-file-info">
                    <el-radio-group v-model="audioSettings.source" class="audio-source-selector">
                      <el-radio value="default">使用默认音乐</el-radio>
                      <el-radio value="custom">自定义音乐文件</el-radio>
                    </el-radio-group>
                  </div>

                  <div v-if="audioSettings.source === 'default'" class="default-audio-info">
                    <div class="audio-file-display">
                      <el-icon class="audio-icon"><Headset /></el-icon>
                      <span class="file-name">mp1.mp3</span>
                      <span class="file-status">（内置音乐）</span>
                    </div>
                  </div>

                  <div v-if="audioSettings.source === 'custom'" class="custom-audio-section">
                    <el-button
                      type="primary"
                      plain
                      @click="selectAudioFile"
                      :disabled="isGenerating"
                    >
                      <el-icon><Upload /></el-icon>
                      选择音频文件
                    </el-button>

                    <div v-if="audioSettings.customFile" class="audio-file-display">
                      <el-icon class="audio-icon"><Headset /></el-icon>
                      <span class="file-name">{{ audioSettings.customFile.name }}</span>
                      <span class="file-size">({{ formatFileSize(audioSettings.customFile.size) }})</span>
                      <el-button
                        type="danger"
                        text
                        @click="removeAudioFile"
                        :disabled="isGenerating"
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                  </div>
                </div>

                <!-- 音量控制 -->
                <div class="audio-volume-section">
                  <div class="volume-label">音量</div>
                  <div class="volume-control">
                    <el-slider
                      v-model="audioSettings.volume"
                      :min="0"
                      :max="1"
                      :step="0.1"
                      :format-tooltip="formatVolumeTooltip"
                      show-input
                      :show-input-controls="false"
                      class="volume-slider"
                    />
                  </div>
                </div>

                <!-- 音频处理提示 -->
                <div class="audio-tips">
                  <el-alert
                    title="音频处理说明"
                    type="info"
                    :closable="false"
                    show-icon
                  >
                    <template #default>
                      <ul class="tips-list">
                        <li>音频长度会自动调整为视频长度</li>
                        <li>音频过短时会循环播放</li>
                        <li>音频过长时会截取前段</li>
                        <li>支持 MP3、WAV、AAC 等常见格式</li>
                      </ul>
                    </template>
                  </el-alert>
                </div>
              </div>
            </div>
          </div>

          <!-- 生成进度 -->
          <div v-if="isGenerating" class="progress-section">
            <!-- 进度阶段指示器 -->
            <div class="progress-stages">
              <div
                v-for="(stage, key) in progressManager.stages"
                :key="key"
                class="stage-indicator"
                :class="{
                  'active': progressManager.currentStage === key,
                  'completed': getStageStatus(key) === 'completed',
                  'pending': getStageStatus(key) === 'pending'
                }"
              >
                <div class="stage-icon">
                  <el-icon v-if="getStageStatus(key) === 'completed'"><Check /></el-icon>
                  <el-icon v-else-if="progressManager.currentStage === key"><Loading /></el-icon>
                  <span v-else class="stage-number">{{ Object.keys(progressManager.stages).indexOf(key) + 1 }}</span>
                </div>
                <div class="stage-label">{{ getStageLabel(key) }}</div>
              </div>
            </div>

            <div class="progress-info">
              <span class="progress-text">{{ progressText }}</span>
              <span class="progress-percent">{{ Math.round(generateProgress) }}%</span>
            </div>
            <el-progress
              :percentage="generateProgress"
              :stroke-width="8"
              :show-text="false"
              class="progress-bar"
            />
          </div>

          <!-- 生成按钮 -->
          <el-button
            type="primary"
            class="generate-button"
            size="large"
            :loading="isGenerating"
            :disabled="!canGenerate"
            @click="generateTransitionVideo"
          >
            <el-icon><VideoPlay /></el-icon>
            {{ isGenerating ? '生成中...' : '生成过渡视频' }}
          </el-button>

          <!-- 预览按钮 -->
<!--          <el-button-->
<!--            v-if="form.startImg && form.endImg && !isGenerating"-->
<!--            type="default"-->
<!--            class="preview-button"-->
<!--            size="large"-->
<!--            @click="previewTransition"-->
<!--          >-->
<!--            <el-icon><View /></el-icon>-->
<!--            预览效果-->
<!--          </el-button>-->
        </div>

        <!-- 右侧内容展示区域 -->
        <div class="content-display">
          <!-- 生成的视频 -->
          <template v-if="generatedVideo">
            <div class="video-container">
              <video
                :src="generatedVideo"
                controls
                class="generated-video"
                @loadedmetadata="handleVideoLoaded"
              />
              <div class="video-info">
                <p class="video-details">
                  时长: {{ videoDuration }}秒 | 效果: {{ getEffectName(transitionEffect) }}
                </p>
                <p class="video-size-info">
                  分辨率: {{ videoSize.width }}×{{ videoSize.height }} ({{ videoSize.aspectRatio }})
                  <el-tag v-if="videoSize.isAutoCalculated" size="small" type="success">智能推荐</el-tag>
                </p>
              </div>
              <div class="video-actions">
                <el-button type="primary" @click="handleDownloadVideo">
                  <el-icon><Download /></el-icon>
                  下载视频
                </el-button>
<!--                <el-button @click="handleShareVideo">-->
<!--                  <el-icon><Share /></el-icon>-->
<!--                  分享-->
<!--                </el-button>-->
                <el-button type="default" @click="regenerateVideo">
                  <el-icon><Refresh /></el-icon>
                  重新生成
                </el-button>
              </div>
            </div>
          </template>

          <!-- 预览模式 -->
          <template v-else-if="isPreviewMode">
            <div class="preview-container">
              <div class="preview-images">
                <div class="image-frame start-frame" :class="{ 'fade-out': previewAnimation }">
                  <img
                    :src="getImagePreview(form.startImg)"
                    alt="首帧"
                    @loadstart="handleImageLoadStart"
                    @load="(e) => handleImageLoad(e, 'preview')"
                  />
                  <div class="frame-label">首帧</div>
                </div>
                <div class="transition-arrow">
                  <el-icon><Right /></el-icon>
                </div>
                <div class="image-frame end-frame" :class="{ 'fade-in': previewAnimation }">
                  <img
                    :src="getImagePreview(form.endImg)"
                    alt="尾帧"
                    @loadstart="handleImageLoadStart"
                    @load="(e) => handleImageLoad(e, 'preview')"
                  />
                  <div class="frame-label">尾帧</div>
                </div>
              </div>
              <div class="preview-controls">
                <el-button @click="playPreview" :disabled="previewAnimation">
                  <el-icon><VideoPlay /></el-icon>
                  播放预览
                </el-button>
                <el-button @click="stopPreview">
                  <el-icon><VideoPause /></el-icon>
                  停止预览
                </el-button>
              </div>
            </div>
          </template>

          <!-- 空状态 -->
          <template v-else>
            <div class="empty-state">
              <el-icon class="empty-icon"><VideoPlay /></el-icon>
              <p class="empty-text">生成的过渡视频将在这里呈现</p>
              <p class="empty-subtext">上传首帧和尾帧图片，设置参数后点击生成</p>
            </div>
          </template>
        </div>
      </div>
    </div>

    <!-- 参考提示词区域 -->
<!--    <div class="reference-tags">-->
<!--      <el-card>-->
<!--        <span class="text-info">过渡效果提示词</span>-->
<!--        <div class="tag-container">-->
<!--          <el-tag-->
<!--            class="tag-info"-->
<!--            v-for="tag in transitionTags"-->
<!--            :key="tag"-->
<!--            @click="addTagToDescription(tag)"-->
<!--          >-->
<!--            {{ tag }}-->
<!--          </el-tag>-->
<!--        </div>-->
<!--      </el-card>-->
<!--    </div>-->
  </div>
</template>

<script setup name="GenVideo">
import { ref, reactive, getCurrentInstance, computed, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Plus, Edit, Delete, QuestionFilled, VideoPlay, Download, Share,
  View, Right, VideoPause, Refresh, Check, Loading, Upload, Headset
} from '@element-plus/icons-vue'

import ffmpegHelper from '@/utils/ffmpegHelper'
import DiagnosticTool from '@/utils/diagnostics'
import electronFileHandler from '@/utils/electronFileHandler'

const { proxy } = getCurrentInstance()

// 表单数据
const form = reactive({
  startImg: null,  // 首帧图片URL
  endImg: null,    // 尾帧图片URL
  startImgFile: null,  // 首帧图片文件对象
  endImgFile: null     // 尾帧图片文件对象
})

// 描述输入框
const description = ref('')
const maxLength = 500

// 视频设置
const videoDuration = ref('5')  // 视频时长
const transitionEffect = ref('crossfade')  // 过渡效果

// 音频设置
const audioSettings = reactive({
  enabled: false,           // 是否启用背景音乐
  source: 'default',        // 音频源：'default' | 'custom'
  customFile: null,         // 自定义音频文件
  volume: 0.5,              // 音量 (0-1)
  duration: 0,              // 音频时长（秒）
  format: ''                // 音频格式
})

// 控制状态
const isGenerating = ref(false)
const generatedVideo = ref('')
const generateProgress = ref(0)
const progressText = ref('')

// 进度管理系统
const progressManager = reactive({
  currentStage: '',
  stages: {
    'init': { min: 0, max: 10, text: '初始化处理引擎...' },
    'prepare': { min: 10, max: 25, text: '准备图片文件...' },
    'process': { min: 25, max: 85, text: '生成过渡效果...' },
    'finalize': { min: 85, max: 95, text: '读取生成的视频...' },
    'complete': { min: 95, max: 100, text: '视频生成完成！' }
  },
  smoothProgress: 0,
  targetProgress: 0,
  progressInterval: null
})

// FFmpeg相关状态
const isFFmpegLoaded = ref(false)
const isFFmpegLoading = ref(false)
const ffmpegError = ref('')
const useLocalProcessing = ref(true)  // 是否使用本地处理（固定为true）

// 预览相关
const isPreviewMode = ref(false)
const previewAnimation = ref(false)

// 文件上传相关
const currentUploadTarget = ref('start')  // 当前上传目标：'start' 或 'end'

// 图片尺寸信息
const imageInfo = reactive({
  startImg: {
    width: 0,
    height: 0,
    aspectRatio: 0,
    orientation: '', // 'landscape', 'portrait', 'square'
    loaded: false
  },
  endImg: {
    width: 0,
    height: 0,
    aspectRatio: 0,
    orientation: '', // 'landscape', 'portrait', 'square'
    loaded: false
  }
})

// 智能视频尺寸设置
const videoSize = reactive({
  width: 1280,
  height: 720,
  aspectRatio: '16:9',
  isAutoCalculated: false,
  recommendedSizes: []
})

// 过渡效果相关标签
const transitionTags = ref([
  "平滑过渡",
  "渐变融合",
  "光影变化",
  "色彩渐变",
  "透明度变化",
  "柔和切换",
  "自然衔接",
  "流畅转换",
  "淡入淡出",
  "交叉溶解",
  "时间缓动",
  "视觉连贯"
])

// 计算属性
const canGenerate = computed(() => {
  return form.startImg && form.endImg && !isGenerating.value
})

// 获取过渡效果名称
const getEffectName = (effect) => {
  const effectNames = {
    fade: '淡入淡出',
    crossfade: '交叉溶解',
    slide: '滑动过渡',
    zoom: '缩放过渡'
  }
  return effectNames[effect] || '淡入淡出'
}

// 进度管理函数
const startProgressManager = () => {
  // 清除之前的定时器
  if (progressManager.progressInterval) {
    clearInterval(progressManager.progressInterval)
  }

  // 重置进度状态
  progressManager.smoothProgress = 0
  progressManager.targetProgress = 0
  progressManager.currentStage = ''

  // 启动平滑进度更新
  progressManager.progressInterval = setInterval(() => {
    const diff = progressManager.targetProgress - progressManager.smoothProgress
    if (Math.abs(diff) > 0.1) {
      // 使用缓动函数实现平滑过渡
      progressManager.smoothProgress += diff * 0.1
      generateProgress.value = Math.round(progressManager.smoothProgress)
    } else {
      progressManager.smoothProgress = progressManager.targetProgress
      generateProgress.value = Math.round(progressManager.smoothProgress)
    }
  }, 50) // 每50ms更新一次，实现平滑动画
}

const stopProgressManager = () => {
  if (progressManager.progressInterval) {
    clearInterval(progressManager.progressInterval)
    progressManager.progressInterval = null
  }
}

const setProgressStage = (stageName, stageProgress = 0) => {
  const stage = progressManager.stages[stageName]
  if (!stage) {
    console.warn(`未知的进度阶段: ${stageName}`)
    return
  }

  progressManager.currentStage = stageName
  progressText.value = stage.text

  // 计算目标进度值
  const stageRange = stage.max - stage.min
  const targetProgress = stage.min + (stageRange * Math.min(stageProgress, 1))
  progressManager.targetProgress = Math.min(targetProgress, 100)

  console.log(`进度阶段: ${stageName}, 阶段进度: ${(stageProgress * 100).toFixed(1)}%, 目标进度: ${progressManager.targetProgress.toFixed(1)}%`)
}

const updateStageProgress = (stageProgress) => {
  if (!progressManager.currentStage) return

  const stage = progressManager.stages[progressManager.currentStage]
  if (!stage) return

  const stageRange = stage.max - stage.min
  const targetProgress = stage.min + (stageRange * Math.min(stageProgress, 1))
  progressManager.targetProgress = Math.min(targetProgress, 100)
}

// 初始化FFmpeg
const initializeFFmpeg = async () => {
  if (isFFmpegLoaded.value || isFFmpegLoading.value) return

  isFFmpegLoading.value = true
  ffmpegError.value = ''

  try {
    const result = await ffmpegHelper.initialize((event) => {
      switch (event.type) {
        case 'status':
          // 如果在生成过程中，使用进度管理系统
          if (isGenerating.value) {
            if (event.message.includes('加载FFmpeg核心文件')) {
              setProgressStage('init', 0.7)
            } else if (event.message.includes('FFmpeg加载完成')) {
              setProgressStage('init', 0.9)
            }
          } else {
            progressText.value = event.message
          }
          break
        case 'progress':
          // 在生成过程中，FFmpeg初始化进度映射到init阶段
          if (isGenerating.value) {
            const initProgress = 0.5 + (event.progress / 100) * 0.4 // 0.5-0.9范围
            setProgressStage('init', initProgress)
          } else {
            generateProgress.value = Math.min(event.progress, 90)
          }
          break
        case 'log':
          console.log('FFmpeg Log:', event.message)
          break
      }
    })

    if (result.success) {
      isFFmpegLoaded.value = true
      if (!isGenerating.value) {
        progressText.value = 'FFmpeg初始化完成'
        ElMessage.success('本地视频处理引擎已就绪')
      }
    } else {
      throw new Error(result.error || '初始化失败')
    }
  } catch (error) {
    console.error('FFmpeg初始化失败:', error)
    ffmpegError.value = error.message || '初始化失败'

    // 提供用户友好的错误提示
    let userMessage = error.message
    if (error.message.includes('SharedArrayBuffer')) {
      userMessage = '浏览器安全设置问题，请尝试刷新页面或使用Chrome浏览器'
    } else if (error.message.includes('网络')) {
      userMessage = '网络连接问题，请检查网络后重试'
    } else if (error.message.includes('浏览器')) {
      userMessage = '浏览器版本过低，请升级到最新版本'
    }

    ElMessage.error(`FFmpeg初始化失败: ${userMessage}`)
  } finally {
    isFFmpegLoading.value = false
    if (!isGenerating.value) {
      generateProgress.value = 0
      progressText.value = ''
    }
  }
}

// 检查浏览器兼容性
const checkBrowserCompatibility = () => {
  const compatibilityCheck = ffmpegHelper.checkBrowserCompatibility()
  if (!compatibilityCheck.compatible) {
    const errorMessage = `浏览器兼容性问题: ${compatibilityCheck.errors.join(', ')}`
    ElMessage.warning(errorMessage)
    ffmpegError.value = errorMessage
    return false
  }
  return true
}

// 运行完整诊断
const runDiagnostics = async () => {
  try {
    const diagnostics = await DiagnosticTool.runDiagnostics()
    const report = DiagnosticTool.formatDiagnosticReport(diagnostics)

    console.log(report)

    if (diagnostics.overall === 'error') {
      ElMessage.error('检测到严重兼容性问题，请查看控制台获取详细信息')
    } else if (diagnostics.overall === 'warning') {
      ElMessage.warning('检测到一些兼容性问题，可能影响功能使用')
    } else {
      ElMessage.success('系统兼容性检查通过')
    }

    return diagnostics
  } catch (error) {
    console.error('诊断检查失败:', error)
    ElMessage.error('诊断检查失败')
    return null
  }
}

// 处理上传点击
const handleUploadClick = (target) => {
  currentUploadTarget.value = target

  // 本地处理模式：直接选择文件
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'image/jpeg,image/jpg,image/png,image/gif'
  input.onchange = (event) => handleFileSelect(event, target)
  input.click()
}

// 处理文件选择（用于本地处理）
const handleFileSelect = (event, target) => {
  const file = event.target.files[0]
  if (!file) return

  // 验证文件类型
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
  if (!allowedTypes.includes(file.type)) {
    ElMessage.error('请选择JPG、PNG或GIF格式的图片')
    return
  }

  // 验证文件大小（10MB限制）
  const maxSize = 10 * 1024 * 1024
  if (file.size > maxSize) {
    ElMessage.error('图片文件大小不能超过10MB')
    return
  }

  try {
    // 创建预览URL
    const previewUrl = URL.createObjectURL(file)

    // 释放之前的URL（如果存在）
    if (target === 'start') {
      if (form.startImg && form.startImg.startsWith('blob:')) {
        URL.revokeObjectURL(form.startImg)
      }
      form.startImg = previewUrl
      form.startImgFile = file
      // 重置图片尺寸信息
      Object.assign(imageInfo.startImg, {
        width: 0,
        height: 0,
        aspectRatio: 0,
        orientation: '',
        loaded: false
      })
    } else {
      if (form.endImg && form.endImg.startsWith('blob:')) {
        URL.revokeObjectURL(form.endImg)
      }
      form.endImg = previewUrl
      form.endImgFile = file
      // 重置图片尺寸信息
      Object.assign(imageInfo.endImg, {
        width: 0,
        height: 0,
        aspectRatio: 0,
        orientation: '',
        loaded: false
      })
    }

    ElMessage.success(`${target === 'start' ? '首帧' : '尾帧'}图片选择成功`)

  } catch (error) {
    console.error('图片处理失败:', error)
    ElMessage.error('图片处理失败，请重新选择')
  }
}

// 获取图片预览URL
const getImagePreview = (fileUrl) => {
  if (!fileUrl) {
    console.warn('getImagePreview: fileUrl is empty')
    return ''
  }

  // 如果是blob URL（本地文件），直接返回
  if (fileUrl.startsWith('blob:')) {
    return fileUrl
  }

  // 如果是完整URL，直接返回
  if (fileUrl.startsWith('http')) {
    return fileUrl
  }

  // 如果是base64数据，直接返回
  if (fileUrl.startsWith('data:')) {
    return fileUrl
  }

  // 如果是相对路径，拼接基础URL
  const baseUrl = import.meta.env.VITE_APP_BASE_API || ''
  const result = fileUrl.startsWith('/') ? `${baseUrl}${fileUrl}` : `${baseUrl}/${fileUrl}`
  return result
}

// 计算图片最佳显示尺寸（长边自适应）
const calculateImageSize = (naturalWidth, naturalHeight, maxWidth, maxHeight) => {
  if (!naturalWidth || !naturalHeight) {
    return { width: 'auto', height: 'auto' }
  }

  const aspectRatio = naturalWidth / naturalHeight
  let displayWidth = naturalWidth
  let displayHeight = naturalHeight

  // 如果图片尺寸超出容器，按长边缩放
  if (displayWidth > maxWidth || displayHeight > maxHeight) {
    if (aspectRatio > 1) {
      // 横向图片，以宽度为准
      displayWidth = Math.min(maxWidth, naturalWidth)
      displayHeight = displayWidth / aspectRatio

      // 如果高度仍然超出，以高度为准重新计算
      if (displayHeight > maxHeight) {
        displayHeight = maxHeight
        displayWidth = displayHeight * aspectRatio
      }
    } else {
      // 纵向图片，以高度为准
      displayHeight = Math.min(maxHeight, naturalHeight)
      displayWidth = displayHeight * aspectRatio

      // 如果宽度仍然超出，以宽度为准重新计算
      if (displayWidth > maxWidth) {
        displayWidth = maxWidth
        displayHeight = displayWidth / aspectRatio
      }
    }
  }

  return {
    width: Math.round(displayWidth) + 'px',
    height: Math.round(displayHeight) + 'px'
  }
}

// 处理图片加载完成事件，设置最佳显示尺寸
const handleImageLoad = (event, containerType = 'upload') => {
  const img = event.target
  const { naturalWidth, naturalHeight } = img

  // 检测图片尺寸信息（根据图片src判断是首帧还是尾帧）
  const imgSrc = img.src
  let target = 'start'
  if (imgSrc === getImagePreview(form.endImg)) {
    target = 'end'
  }

  // 检测并记录图片尺寸信息
  detectImageDimensions(img, target)

  let maxWidth, maxHeight

  if (containerType === 'upload') {
    // 上传预览区域的最大尺寸
    maxWidth = 200
    maxHeight = 140
  } else if (containerType === 'preview') {
    // 右侧预览区域的最大尺寸
    maxWidth = 280
    maxHeight = 230
  }

  const { width, height } = calculateImageSize(naturalWidth, naturalHeight, maxWidth, maxHeight)

  // 设置图片显示尺寸和状态
  img.style.width = width
  img.style.height = height
  img.classList.remove('loading')
  img.classList.add('loaded')

  console.log(`图片加载完成 [${containerType}]:`, {
    natural: `${naturalWidth}x${naturalHeight}`,
    display: `${width}x${height}`,
    aspectRatio: (naturalWidth / naturalHeight).toFixed(2)
  })
}

// 处理图片开始加载事件
const handleImageLoadStart = (event) => {
  const img = event.target
  img.classList.add('loading')
  img.classList.remove('loaded')
}

// 检测图片尺寸信息
const detectImageDimensions = (img, target) => {
  const { naturalWidth, naturalHeight } = img
  const aspectRatio = naturalWidth / naturalHeight

  let orientation = 'square'
  if (aspectRatio > 1.1) {
    orientation = 'landscape'
  } else if (aspectRatio < 0.9) {
    orientation = 'portrait'
  }

  const info = {
    width: naturalWidth,
    height: naturalHeight,
    aspectRatio: aspectRatio,
    orientation: orientation,
    loaded: true
  }

  if (target === 'start') {
    Object.assign(imageInfo.startImg, info)
  } else {
    Object.assign(imageInfo.endImg, info)
  }

  console.log(`图片尺寸检测 [${target}]:`, info)

  // 如果两张图片都已加载，计算最佳视频尺寸
  if (imageInfo.startImg.loaded && imageInfo.endImg.loaded) {
    calculateOptimalVideoSize()
  }
}

// 计算最佳视频尺寸
const calculateOptimalVideoSize = () => {
  const { startImg, endImg } = imageInfo

  console.log('开始计算最佳视频尺寸:', { startImg, endImg })

  // 定义常见视频分辨率标准
  const videoStandards = [
    // 16:9 横屏标准
    { width: 3840, height: 2160, ratio: '16:9', name: '4K UHD' },
    { width: 2560, height: 1440, ratio: '16:9', name: '2K QHD' },
    { width: 1920, height: 1080, ratio: '16:9', name: '1080p FHD' },
    { width: 1280, height: 720, ratio: '16:9', name: '720p HD' },
    { width: 854, height: 480, ratio: '16:9', name: '480p' },

    // 4:3 传统标准
    { width: 1024, height: 768, ratio: '4:3', name: '4:3 XGA' },
    { width: 800, height: 600, ratio: '4:3', name: '4:3 SVGA' },

    // 1:1 正方形
    { width: 1080, height: 1080, ratio: '1:1', name: '1:1 Square' },
    { width: 720, height: 720, ratio: '1:1', name: '1:1 Square SD' },

    // 9:16 竖屏标准
    { width: 1080, height: 1920, ratio: '9:16', name: '9:16 Portrait FHD' },
    { width: 720, height: 1280, ratio: '9:16', name: '9:16 Portrait HD' },

    // 21:9 超宽屏
    { width: 2560, height: 1080, ratio: '21:9', name: '21:9 UltraWide' },
    { width: 1920, height: 810, ratio: '21:9', name: '21:9 Cinema' }
  ]

  // 分析图片特征
  const avgWidth = (startImg.width + endImg.width) / 2
  const avgHeight = (startImg.height + endImg.height) / 2
  const avgAspectRatio = avgWidth / avgHeight

  // 确定主要方向
  let primaryOrientation = 'landscape'
  if (startImg.orientation === endImg.orientation) {
    primaryOrientation = startImg.orientation
  } else {
    // 如果方向不一致，选择平均宽高比决定
    if (avgAspectRatio > 1.1) {
      primaryOrientation = 'landscape'
    } else if (avgAspectRatio < 0.9) {
      primaryOrientation = 'portrait'
    } else {
      primaryOrientation = 'square'
    }
  }

  // 计算推荐尺寸
  const recommendations = []

  for (const standard of videoStandards) {
    const standardRatio = standard.width / standard.height
    const ratioDiff = Math.abs(standardRatio - avgAspectRatio)

    // 检查尺寸是否合适
    const maxDimension = Math.max(avgWidth, avgHeight)
    const minDimension = Math.min(avgWidth, avgHeight)

    // 不要超过原图尺寸太多，也不要太小
    const sizeScore = calculateSizeScore(standard, maxDimension, minDimension)
    const ratioScore = 1 / (1 + ratioDiff * 2) // 宽高比匹配度

    if (sizeScore > 0.3) { // 只考虑合适的尺寸
      recommendations.push({
        ...standard,
        score: sizeScore * 0.6 + ratioScore * 0.4,
        ratioDiff: ratioDiff,
        sizeScore: sizeScore,
        ratioScore: ratioScore
      })
    }
  }

  // 按分数排序
  recommendations.sort((a, b) => b.score - a.score)

  // 选择最佳尺寸
  const bestSize = recommendations[0]

  if (bestSize) {
    videoSize.width = bestSize.width
    videoSize.height = bestSize.height
    videoSize.aspectRatio = bestSize.ratio
    videoSize.isAutoCalculated = true
    videoSize.recommendedSizes = recommendations.slice(0, 5) // 保存前5个推荐

    console.log('计算出的最佳视频尺寸:', bestSize)
    console.log('所有推荐尺寸:', recommendations.slice(0, 5))

    ElMessage.success(`智能推荐视频尺寸: ${bestSize.width}x${bestSize.height} (${bestSize.name})`)
  } else {
    // 如果没有合适的标准尺寸，使用自定义计算
    const customSize = calculateCustomVideoSize(avgWidth, avgHeight, avgAspectRatio)
    Object.assign(videoSize, customSize)

    console.log('使用自定义计算的视频尺寸:', customSize)
    ElMessage.info(`使用自定义视频尺寸: ${customSize.width}x${customSize.height}`)
  }
}

// 计算尺寸适配分数
const calculateSizeScore = (standard, maxDimension, minDimension) => {
  const standardMax = Math.max(standard.width, standard.height)
  const standardMin = Math.min(standard.width, standard.height)

  // 避免过度放大或缩小
  const maxScale = standardMax / maxDimension
  const minScale = standardMin / minDimension

  // 理想的缩放范围是 0.5 到 2.0
  let score = 0

  if (maxScale >= 0.5 && maxScale <= 2.0 && minScale >= 0.5 && minScale <= 2.0) {
    // 在理想范围内，越接近1.0分数越高
    const avgScale = (maxScale + minScale) / 2
    score = 1 / (1 + Math.abs(avgScale - 1.0))
  } else if (maxScale < 0.5 || minScale < 0.5) {
    // 过度放大，分数较低
    score = Math.max(maxScale, minScale) * 0.5
  } else {
    // 过度缩小，分数较低
    score = 1 / Math.max(maxScale, minScale) * 0.5
  }

  return Math.max(0, Math.min(1, score))
}

// 计算自定义视频尺寸
const calculateCustomVideoSize = (avgWidth, avgHeight, avgAspectRatio) => {
  // 限制最大和最小尺寸
  const maxSize = 3840 // 4K
  const minSize = 480  // 最小尺寸

  let targetWidth = avgWidth
  let targetHeight = avgHeight

  // 确保尺寸在合理范围内
  const maxDimension = Math.max(targetWidth, targetHeight)
  const minDimension = Math.min(targetWidth, targetHeight)

  if (maxDimension > maxSize) {
    const scale = maxSize / maxDimension
    targetWidth *= scale
    targetHeight *= scale
  }

  if (minDimension < minSize) {
    const scale = minSize / minDimension
    targetWidth *= scale
    targetHeight *= scale
  }

  // 调整为偶数（视频编码要求）
  targetWidth = Math.round(targetWidth / 2) * 2
  targetHeight = Math.round(targetHeight / 2) * 2

  // 确定宽高比描述
  let ratioDesc = 'Custom'
  const ratio = targetWidth / targetHeight
  if (Math.abs(ratio - 16/9) < 0.1) ratioDesc = '16:9'
  else if (Math.abs(ratio - 4/3) < 0.1) ratioDesc = '4:3'
  else if (Math.abs(ratio - 1) < 0.1) ratioDesc = '1:1'
  else if (Math.abs(ratio - 9/16) < 0.1) ratioDesc = '9:16'

  return {
    width: targetWidth,
    height: targetHeight,
    aspectRatio: ratioDesc,
    isAutoCalculated: true,
    recommendedSizes: [{
      width: targetWidth,
      height: targetHeight,
      ratio: ratioDesc,
      name: 'Custom Size',
      score: 1.0
    }]
  }
}

// 删除图片
const removeImage = (target) => {
  if (target === 'start') {
    // 释放URL对象
    if (form.startImg && form.startImg.startsWith('blob:')) {
      URL.revokeObjectURL(form.startImg)
    }
    form.startImg = null
    form.startImgFile = null
    // 重置图片尺寸信息
    Object.assign(imageInfo.startImg, {
      width: 0,
      height: 0,
      aspectRatio: 0,
      orientation: '',
      loaded: false
    })
  } else {
    // 释放URL对象
    if (form.endImg && form.endImg.startsWith('blob:')) {
      URL.revokeObjectURL(form.endImg)
    }
    form.endImg = null
    form.endImgFile = null
    // 重置图片尺寸信息
    Object.assign(imageInfo.endImg, {
      width: 0,
      height: 0,
      aspectRatio: 0,
      orientation: '',
      loaded: false
    })
  }

  // 如果两张图片都被删除，重置视频尺寸设置
  if (!imageInfo.startImg.loaded && !imageInfo.endImg.loaded) {
    videoSize.width = 1280
    videoSize.height = 720
    videoSize.aspectRatio = '16:9'
    videoSize.isAutoCalculated = false
    videoSize.recommendedSizes = []
  }

  ElMessage.success(`${target === 'start' ? '首帧' : '尾帧'}图片已删除`)
}

// 音频相关方法
// 音频启用状态改变
const onAudioEnabledChange = (enabled) => {
  if (!enabled) {
    // 禁用音频时清理自定义文件
    audioSettings.customFile = null
  }
}

// 选择音频文件
const selectAudioFile = async () => {
  try {
    const files = await electronFileHandler.selectFiles({
      multiple: false,
      filters: [
        { name: '音频文件', extensions: ['mp3', 'wav', 'aac', 'm4a', 'ogg'] },
        { name: '所有文件', extensions: ['*'] }
      ]
    })

    if (files.length > 0) {
      const file = files[0]

      // 验证文件类型
      const allowedTypes = ['audio/mpeg', 'audio/wav', 'audio/aac', 'audio/mp4', 'audio/ogg']
      if (!allowedTypes.includes(file.type) && !file.name.match(/\.(mp3|wav|aac|m4a|ogg)$/i)) {
        ElMessage.error('请选择支持的音频格式（MP3、WAV、AAC、M4A、OGG）')
        return
      }

      // 验证文件大小（限制50MB）
      const maxSize = 50 * 1024 * 1024
      if (file.size > maxSize) {
        ElMessage.error('音频文件大小不能超过50MB')
        return
      }

      audioSettings.customFile = file
      ElMessage.success('音频文件选择成功')

      // 尝试获取音频时长（如果可能）
      try {
        const duration = await getAudioDuration(file)
        audioSettings.duration = duration
      } catch (error) {
        console.warn('无法获取音频时长:', error)
      }
    }
  } catch (error) {
    console.error('音频文件选择失败:', error)
    ElMessage.error(`音频文件选择失败: ${error.message}`)
  }
}

// 移除音频文件
const removeAudioFile = () => {
  audioSettings.customFile = null
  audioSettings.duration = 0
  ElMessage.success('音频文件已移除')
}

// 获取音频时长
const getAudioDuration = (file) => {
  return new Promise((resolve, reject) => {
    const audio = new Audio()
    const url = URL.createObjectURL(file)

    audio.addEventListener('loadedmetadata', () => {
      URL.revokeObjectURL(url)
      resolve(audio.duration)
    })

    audio.addEventListener('error', (error) => {
      URL.revokeObjectURL(url)
      reject(error)
    })

    audio.src = url
  })
}

// 格式化音量提示
const formatVolumeTooltip = (value) => {
  return `${Math.round(value * 100)}%`
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 添加标签到描述
const addTagToDescription = (tag) => {
  if (description.value.length + tag.length + 1 <= maxLength) {
    description.value += (description.value ? '，' : '') + tag
  }
}

// 获取方向标签类型
const getOrientationTagType = (orientation) => {
  switch (orientation) {
    case 'landscape': return 'primary'
    case 'portrait': return 'warning'
    case 'square': return 'success'
    default: return 'info'
  }
}

// 获取方向文本
const getOrientationText = (orientation) => {
  switch (orientation) {
    case 'landscape': return '横向'
    case 'portrait': return '纵向'
    case 'square': return '正方形'
    default: return '未知'
  }
}

// 选择视频尺寸
const selectVideoSize = (size) => {
  videoSize.width = size.width
  videoSize.height = size.height
  videoSize.aspectRatio = size.ratio
  ElMessage.success(`已选择视频尺寸: ${size.width}×${size.height} (${size.name})`)
}

// 验证视频尺寸设置
const validateVideoSize = () => {
  const { width, height } = videoSize

  // 检查尺寸是否为正数
  if (width <= 0 || height <= 0) {
    ElMessage.error('视频尺寸设置无效')
    return false
  }

  // 检查尺寸是否为偶数（视频编码要求）
  if (width % 2 !== 0 || height % 2 !== 0) {
    // 自动调整为偶数
    videoSize.width = Math.round(width / 2) * 2
    videoSize.height = Math.round(height / 2) * 2
    console.log(`视频尺寸已调整为偶数: ${videoSize.width}×${videoSize.height}`)
  }

  // 检查尺寸限制
  const maxSize = 4096 // 4K限制
  const minSize = 240  // 最小尺寸

  if (Math.max(width, height) > maxSize) {
    ElMessage.error(`视频尺寸过大，最大支持 ${maxSize}px`)
    return false
  }

  if (Math.min(width, height) < minSize) {
    ElMessage.error(`视频尺寸过小，最小支持 ${minSize}px`)
    return false
  }

  // 检查极端宽高比
  const aspectRatio = width / height
  if (aspectRatio > 10 || aspectRatio < 0.1) {
    ElMessage.warning('检测到极端宽高比，可能影响视频质量')
  }

  return true
}

// 重置视频尺寸为默认值
const resetVideoSize = () => {
  videoSize.width = 1280
  videoSize.height = 720
  videoSize.aspectRatio = '16:9'
  videoSize.isAutoCalculated = false
  videoSize.recommendedSizes = []
  ElMessage.info('已重置为默认视频尺寸: 1280×720 (16:9)')
}

// 获取阶段状态
const getStageStatus = (stageKey) => {
  const stageKeys = Object.keys(progressManager.stages)
  const currentIndex = stageKeys.indexOf(progressManager.currentStage)
  const stageIndex = stageKeys.indexOf(stageKey)

  if (stageIndex < currentIndex) {
    return 'completed'
  } else if (stageIndex === currentIndex) {
    return 'active'
  } else {
    return 'pending'
  }
}

// 获取阶段标签
const getStageLabel = (stageKey) => {
  const labels = {
    'init': '初始化',
    'prepare': '准备文件',
    'process': '生成视频',
    'finalize': '处理完成',
    'complete': '完成'
  }
  return labels[stageKey] || stageKey
}



// 生成过渡视频
const generateTransitionVideo = async () => {
  if (!form.startImg || !form.endImg) {
    ElMessage.warning('请先上传首帧和尾帧图片')
    return
  }

  // 检查处理方式
  if (useLocalProcessing.value && (!form.startImgFile || !form.endImgFile)) {
    ElMessage.warning('本地处理模式需要重新选择图片文件')
    return
  }

  // 检查图片尺寸信息是否已加载
  if (!imageInfo.startImg.loaded || !imageInfo.endImg.loaded) {
    ElMessage.warning('图片尺寸信息尚未加载完成，请稍等')
    return
  }

  // 验证视频尺寸设置
  if (!validateVideoSize()) {
    return
  }

  isGenerating.value = true
  isPreviewMode.value = false

  // 启动进度管理系统
  startProgressManager()

  // 立即显示初始进度
  setProgressStage('init', 0.3)

  try {
    if (useLocalProcessing.value && isFFmpegLoaded.value) {
      // 使用本地FFmpeg处理
      await generateVideoLocally()
    } else {
      // 只支持本地处理，如果FFmpeg未加载则提示错误
      throw new Error('本地处理引擎未就绪，请等待加载完成或刷新页面重试')
    }
  } catch (error) {
    console.error('视频生成错误:', error)
    ElMessage.error(`视频生成失败: ${error.message}`)
  } finally {
    // 延迟清理，让用户看到完成状态
    setTimeout(() => {
      stopProgressManager()
      isGenerating.value = false
      generateProgress.value = 0
      progressText.value = ''
    }, 1500)
  }
}

// 本地生成视频
const generateVideoLocally = async () => {
  try {
    // 阶段1：初始化处理引擎
    setProgressStage('init', 0.5)

    if (!isFFmpegLoaded.value) {
      await initializeFFmpeg()
    }

    // 完成初始化阶段
    setProgressStage('init', 1.0)

    // 等待一小段时间让用户看到进度变化
    await new Promise(resolve => setTimeout(resolve, 200))

    const options = {
      duration: parseInt(videoDuration.value),
      effect: transitionEffect.value,
      fps: 30,
      width: videoSize.width,
      height: videoSize.height,
      enableAudio: audioSettings.enabled,
      audioFile: audioSettings.source === 'custom' ? audioSettings.customFile : null,
      audioVolume: audioSettings.volume
    }

    console.log('使用视频生成参数:', options)

    // 阶段2：准备文件
    setProgressStage('prepare', 0.2)

    // 创建进度回调函数
    const originalCallback = ffmpegHelper.progressCallback
    ffmpegHelper.progressCallback = (event) => {
      // 调用原始回调
      if (originalCallback) {
        originalCallback(event)
      }

      // 处理我们的进度更新
      switch (event.type) {
        case 'status':
          if (event.message.includes('准备图片文件')) {
            setProgressStage('prepare', 0.4)
          } else if (event.message.includes('准备音频文件')) {
            setProgressStage('prepare', 0.6)
          } else if (event.message.includes('处理音频长度')) {
            setProgressStage('prepare', 0.8)
          } else if (event.message.includes('生成带音频的过渡效果') || event.message.includes('生成过渡效果')) {
            setProgressStage('process', 0.1)
          } else if (event.message.includes('读取生成的视频')) {
            setProgressStage('finalize', 0.5)
          } else if (event.message.includes('视频生成完成')) {
            setProgressStage('complete', 1.0)
          } else if (event.message.includes('音频处理失败')) {
            // 音频处理失败时继续，但给出提示
            console.warn('音频处理失败，将生成无音频视频')
          }
          break
        case 'progress':
          // FFmpeg的进度主要在process阶段
          if (progressManager.currentStage === 'process') {
            const ffmpegProgress = Math.min(event.progress / 100, 1)
            updateStageProgress(ffmpegProgress)
          }
          break
      }
    }

    const result = await ffmpegHelper.generateTransitionVideo(
      form.startImgFile,
      form.endImgFile,
      options
    )

    // 恢复原始回调
    ffmpegHelper.progressCallback = originalCallback

    // 确保完成阶段
    setProgressStage('complete', 1.0)

    // 释放之前的视频URL
    if (generatedVideo.value && generatedVideo.value.startsWith('blob:')) {
      URL.revokeObjectURL(generatedVideo.value)
    }

    generatedVideo.value = result.videoUrl
    ElMessage.success(`过渡视频生成成功！文件大小: ${(result.size / 1024 / 1024).toFixed(2)}MB`)

  } catch (error) {
    console.error('本地视频生成失败:', error)
    ElMessage.error('本地视频生成失败，请检查图片格式或重试')
    throw error
  }
}

// 预览过渡效果
const previewTransition = () => {
  isPreviewMode.value = true
  generatedVideo.value = ''
}

// 播放预览动画
const playPreview = () => {
  if (!form.startImg || !form.endImg) {
    ElMessage.warning('请先上传首帧和尾帧图片')
    return
  }

  previewAnimation.value = true
  const duration = parseInt(videoDuration.value) || 5
  setTimeout(() => {
    previewAnimation.value = false
  }, duration * 1000)
}

// 停止预览
const stopPreview = () => {
  previewAnimation.value = false
  isPreviewMode.value = false
}

// 重新生成视频
const regenerateVideo = () => {
  generatedVideo.value = ''
  generateTransitionVideo()
}

// 视频加载完成
const handleVideoLoaded = () => {
  console.log('视频加载完成')
}

// 下载视频
const handleDownloadVideo = () => {
  if (generatedVideo.value) {
    const a = document.createElement('a')
    a.href = generatedVideo.value
    a.download = `transition-video-${Date.now()}.mp4`
    a.click()
    ElMessage.success('开始下载视频')
  }
}

// 分享视频
const handleShareVideo = () => {
  if (navigator.share && generatedVideo.value) {
    navigator.share({
      title: '我生成的过渡视频',
      url: generatedVideo.value
    }).catch(err => {
      console.log('分享失败:', err)
      copyToClipboard()
    })
  } else {
    copyToClipboard()
  }
}

// 复制到剪贴板
const copyToClipboard = () => {
  if (navigator.clipboard && navigator.clipboard.writeText) {
    navigator.clipboard.writeText(generatedVideo.value).then(() => {
      ElMessage.success('视频链接已复制到剪贴板')
    }).catch(() => {
      fallbackCopyToClipboard()
    })
  } else {
    fallbackCopyToClipboard()
  }
}

// 兼容性复制方法
const fallbackCopyToClipboard = () => {
  const textArea = document.createElement('textarea')
  textArea.value = generatedVideo.value
  document.body.appendChild(textArea)
  textArea.focus()
  textArea.select()
  try {
    document.execCommand('copy')
    ElMessage.success('视频链接已复制到剪贴板')
  } catch (err) {
    ElMessage.error('复制失败，请手动复制链接')
  }
  document.body.removeChild(textArea)
}

// 组件挂载时初始化
onMounted(async () => {
  // 运行完整诊断
  const diagnostics = await runDiagnostics()

  // 如果诊断通过，继续初始化
  if (diagnostics && diagnostics.overall !== 'error') {
    // 检查浏览器兼容性
    if (checkBrowserCompatibility()) {
      // 如果支持本地处理，预加载FFmpeg
      if (useLocalProcessing.value) {
        try {
          await initializeFFmpeg()
        } catch (error) {
          console.warn('FFmpeg预加载失败，将在需要时重试:', error)
        }
      }
    }
  } else {
    console.warn('诊断检查未通过，跳过FFmpeg初始化')
  }
})

// 组件卸载时清理
onUnmounted(() => {
  // 停止进度管理器
  stopProgressManager()

  // 释放视频URL
  if (generatedVideo.value && generatedVideo.value.startsWith('blob:')) {
    URL.revokeObjectURL(generatedVideo.value)
  }

  // 释放图片URL
  if (form.startImg && form.startImg.startsWith('blob:')) {
    URL.revokeObjectURL(form.startImg)
  }
  if (form.endImg && form.endImg.startsWith('blob:')) {
    URL.revokeObjectURL(form.endImg)
  }
})

// 组件卸载时清理资源
onUnmounted(() => {
  // 释放图片URL对象
  if (form.startImg && form.startImg.startsWith('blob:')) {
    URL.revokeObjectURL(form.startImg)
  }
  if (form.endImg && form.endImg.startsWith('blob:')) {
    URL.revokeObjectURL(form.endImg)
  }
  if (generatedVideo.value && generatedVideo.value.startsWith('blob:')) {
    URL.revokeObjectURL(generatedVideo.value)
  }

  // 终止FFmpeg实例（如果需要）
  // ffmpegHelper.terminate()
})
</script>

<style scoped lang="scss">
.ai-create-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f7fa;
  color: #303133;
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.control-panel {
  width: 30%;
  padding: 16px;
  background-color: #ffffff;
  border-right: 1px solid #dcdfe6;
  overflow-y: auto;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

.panel-header {
  margin-bottom: 20px;

  .panel-title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    margin: 0;
  }
}

.content-display {
  width: 70%;
  background-color: #f5f7fa;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: auto;
}

.control-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 14px;
  margin-bottom: 12px;
  color: #303133;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;

  .help-icon {
    color: #909399;
    cursor: help;
  }
}

.upload-area {
  .upload-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 12px;
  }

  .upload-item {
    .upload-placeholder {
      border: 2px dashed #dcdfe6;
      border-radius: 8px;
      padding: 20px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      background-color: #fafafa;
      position: relative;
      min-height: 120px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      &:hover {
        border-color: #409eff;
        background-color: #f0f9ff;
      }

      &.has-image {
        padding: 8px;
        border: 2px solid #409eff;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 120px;
        max-height: 160px;
        position: relative;

        .preview-image {
          max-width: 100%;
          max-height: 100%;
          width: auto;
          height: auto;
          object-fit: contain;
          border-radius: 6px;
          display: block;
          transition: all 0.3s ease;
          opacity: 0;

          /* 图片加载完成后显示 */
          &[style*="width"] {
            opacity: 1;
          }

          /* 加载状态 */
          &.loading {
            opacity: 0.5;
          }

          &.loaded {
            opacity: 1;
          }

          /* 处理极端比例的图片 */
          &[style*="aspect-ratio"] {
            max-width: calc(100% - 16px);
            max-height: calc(100% - 16px);
          }
        }

        .image-overlay {
          position: absolute;
          top: 8px;
          left: 8px;
          right: 8px;
          bottom: 8px;
          background: rgba(0, 0, 0, 0.6);
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 8px;
          opacity: 0;
          transition: opacity 0.3s ease;
          border-radius: 6px;
        }

        &:hover .image-overlay {
          opacity: 1;
        }
      }

      .upload-icon {
        font-size: 24px;
        color: #c0c4cc;
        margin-bottom: 8px;
      }

      .upload-text {
        font-size: 12px;
        color: #606266;
        margin-bottom: 4px;
      }

      .upload-subtext {
        font-size: 10px;
        color: #909399;
      }
    }
  }
}

.upload-tips {
  margin-top: 12px;

  :deep(.el-alert__content) {
    p {
      margin: 2px 0;
      font-size: 12px;
    }
  }
}

// 图片尺寸信息样式
.image-info-section {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;

  .image-info-grid {
    display: grid;
    gap: 12px;
    margin-top: 12px;
  }

  .image-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;

    .info-label {
      font-weight: 500;
      color: #495057;
      font-size: 14px;
    }

    .info-content {
      display: flex;
      align-items: center;
      gap: 8px;

      .size-text {
        font-family: 'Monaco', 'Menlo', monospace;
        font-weight: 600;
        color: #007bff;
      }

      .ratio-text {
        font-size: 12px;
        color: #6c757d;
      }
    }
  }
}

// 智能视频尺寸设置样式
.video-size-section {
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;

  .section-title {
    color: white;
    margin-bottom: 12px;

    .help-icon {
      color: rgba(255, 255, 255, 0.8);
    }
  }

  .video-size-info {
    .current-size {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;

      .size-label {
        font-weight: 500;
      }

      .size-value {
        font-family: 'Monaco', 'Menlo', monospace;
        font-weight: 600;
        font-size: 16px;
      }
    }

    .size-options {
      .options-label {
        font-size: 14px;
        opacity: 0.9;
        margin-bottom: 8px;
        display: block;
      }

      .size-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        // 使用 :deep() 确保样式能够穿透 Element Plus 组件
        :deep(.el-button) {
          font-size: 12px !important;
          padding: 4px 8px !important;
          border-color: rgba(255, 255, 255, 0.5) !important;
          color: rgba(255, 255, 255, 0.95) !important;
          background-color: rgba(255, 255, 255, 0.1) !important;
          transition: all 0.3s ease !important;
          border-width: 1px !important;
          border-style: solid !important;

          // 确保文字颜色在所有状态下都可见
          span {
            color: rgba(255, 255, 255, 0.95) !important;
          }

          &:hover {
            background-color: rgba(255, 255, 255, 0.2) !important;
            border-color: rgba(255, 255, 255, 0.8) !important;
            color: white !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

            span {
              color: white !important;
            }
          }

          &:active {
            background-color: rgba(255, 255, 255, 0.3) !important;
            transform: translateY(0);
          }

          &:focus {
            border-color: rgba(255, 255, 255, 0.8) !important;
            color: white !important;
            outline: none !important;

            span {
              color: white !important;
            }
          }

          // 针对 plain 按钮的特殊处理
          &.is-plain {
            background-color: rgba(255, 255, 255, 0.1) !important;
            border-color: rgba(255, 255, 255, 0.5) !important;
            color: rgba(255, 255, 255, 0.95) !important;

            span {
              color: rgba(255, 255, 255, 0.95) !important;
            }

            &:hover {
              background-color: rgba(255, 255, 255, 0.2) !important;
              border-color: rgba(255, 255, 255, 0.8) !important;
              color: white !important;

              span {
                color: white !important;
              }
            }

            &:focus {
              background-color: rgba(255, 255, 255, 0.15) !important;
              border-color: rgba(255, 255, 255, 0.8) !important;
              color: white !important;

              span {
                color: white !important;
              }
            }
          }

          // 针对特定按钮类的样式
          &.size-option-btn {
            background-color: rgba(255, 255, 255, 0.12) !important;
            border-color: rgba(255, 255, 255, 0.6) !important;
            color: white !important;

            span {
              color: white !important;
            }

            &:hover {
              background-color: rgba(255, 255, 255, 0.25) !important;
              border-color: rgba(255, 255, 255, 0.9) !important;
              color: white !important;

              span {
                color: white !important;
              }
            }
          }

          &.reset-size-btn {
            background-color: rgba(255, 255, 255, 0.08) !important;
            border-color: rgba(255, 255, 255, 0.4) !important;
            color: rgba(255, 255, 255, 0.9) !important;

            span {
              color: rgba(255, 255, 255, 0.9) !important;
            }

            &:hover {
              background-color: rgba(255, 255, 255, 0.18) !important;
              border-color: rgba(255, 255, 255, 0.7) !important;
              color: white !important;

              span {
                color: white !important;
              }
            }
          }
        }
      }
    }
  }
}

.description-input {
  background-color: #ffffff;
  border-color: #dcdfe6;
  color: #303133;

  :deep(.el-textarea__inner) {
    border-radius: 8px;
  }
}

.word-count {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;

  .expand-btn {
    color: #409eff;
    padding: 0;

    .el-icon {
      margin-right: 4px;
    }
  }

  .count-text {
    font-size: 12px;
    color: #909399;
  }
}

.duration-selector {
  width: 100%;

  :deep(.el-radio-button__inner) {
    border-radius: 6px;
    margin-right: 8px;
  }
}

.effect-selector {
  width: 100%;
}

.processing-method-selector {
  width: 100%;

  :deep(.el-radio-button__inner) {
    border-radius: 6px;
    margin-right: 8px;

    .loading-text {
      font-size: 12px;
      color: #909399;
      margin-left: 4px;
    }

    .error-text {
      font-size: 12px;
      color: #f56c6c;
      margin-left: 4px;
    }
  }
}

.ffmpeg-status {
  margin-top: 12px;

  :deep(.el-alert) {
    padding: 8px 12px;

    .el-alert__title {
      font-size: 12px;
    }
  }
}

.progress-section {
  margin-bottom: 16px;

  // 进度阶段指示器
  .progress-stages {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    padding: 0 8px;

    .stage-indicator {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;
      position: relative;

      &:not(:last-child)::after {
        content: '';
        position: absolute;
        top: 16px;
        left: 60%;
        right: -40%;
        height: 2px;
        background-color: #e4e7ed;
        transition: background-color 0.3s ease;
      }

      &.completed:not(:last-child)::after {
        background-color: #67c23a;
      }

      .stage-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        transition: all 0.3s ease;
        position: relative;
        z-index: 1;

        .stage-number {
          font-size: 14px;
          font-weight: 600;
        }

        .el-icon {
          font-size: 16px;
        }
      }

      .stage-label {
        font-size: 12px;
        text-align: center;
        transition: color 0.3s ease;
        white-space: nowrap;
      }

      // 待处理状态
      &.pending {
        .stage-icon {
          background-color: #f5f7fa;
          border: 2px solid #e4e7ed;
          color: #909399;
        }

        .stage-label {
          color: #909399;
        }
      }

      // 进行中状态
      &.active {
        .stage-icon {
          background-color: #409eff;
          border: 2px solid #409eff;
          color: white;
          animation: pulse 2s infinite;

          .el-icon {
            animation: spin 1s linear infinite;
          }
        }

        .stage-label {
          color: #409eff;
          font-weight: 600;
        }
      }

      // 已完成状态
      &.completed {
        .stage-icon {
          background-color: #67c23a;
          border: 2px solid #67c23a;
          color: white;
        }

        .stage-label {
          color: #67c23a;
          font-weight: 500;
        }
      }
    }
  }

  .progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    .progress-text {
      font-size: 14px;
      color: #606266;
      font-weight: 500;
      transition: color 0.3s ease;

      // 添加文字动画效果
      &:not(:empty) {
        animation: textPulse 2s ease-in-out infinite;
      }
    }

    .progress-percent {
      font-size: 14px;
      font-weight: 600;
      color: #409eff;
      font-family: 'Monaco', 'Menlo', monospace;
      transition: all 0.3s ease;

      // 进度数字的动画效果
      &:not(:empty) {
        animation: numberGlow 1.5s ease-in-out infinite alternate;
      }
    }
  }

  .progress-bar {
    :deep(.el-progress-bar__outer) {
      border-radius: 6px;
      background-color: #f0f2f5;
      overflow: hidden;
      position: relative;

      // 添加背景动画
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        animation: shimmer 2s infinite;
      }
    }

    :deep(.el-progress-bar__inner) {
      border-radius: 6px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;

      // 进度条内部光效
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg,
          transparent 0%,
          rgba(255, 255, 255, 0.2) 50%,
          transparent 100%);
        animation: progressGlow 2s ease-in-out infinite;
      }
    }
  }
}

// 进度相关动画
@keyframes textPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes numberGlow {
  0% {
    color: #409eff;
    text-shadow: 0 0 5px rgba(64, 158, 255, 0.3);
  }
  100% {
    color: #5daf34;
    text-shadow: 0 0 8px rgba(93, 175, 52, 0.5);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes progressGlow {
  0%, 100% {
    opacity: 0.3;
    transform: translateX(-100%);
  }
  50% {
    opacity: 0.8;
    transform: translateX(100%);
  }
}

.generate-button {
  width: 100%;
  margin-top: 16px;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  }

  &:disabled {
    background: #c0c4cc;
    cursor: not-allowed;
  }

  .el-icon {
    margin-right: 8px;
  }
}

.preview-button {
  width: 100%;
  margin-top: 12px;
  height: 40px;
  border-radius: 20px;

  .el-icon {
    margin-right: 8px;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  text-align: center;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  color: #c0c4cc;
}

.empty-text {
  font-size: 16px;
  margin-bottom: 8px;
}

.empty-subtext {
  font-size: 14px;
  color: #c0c4cc;
}

.video-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  max-width: 90%;
}

.generated-video {
  max-width: 100%;
  max-height: 70vh;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.video-info {
  text-align: center;

  .video-details {
    font-size: 14px;
    color: #606266;
    margin: 0;
  }
}

.video-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
}

.preview-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  max-width: 90%;
}

.preview-images {
  display: flex;
  align-items: center;
  gap: 24px;

  .image-frame {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.5s ease;
    display: flex;
    justify-content: center;
    align-items: center;
    min-width: 200px;
    min-height: 150px;
    max-width: 300px;
    max-height: 250px;
    background-color: #f8f9fa;

    img {
      max-width: 100%;
      max-height: 100%;
      width: auto;
      height: auto;
      object-fit: contain;
      display: block;
      border-radius: 8px;
      transition: all 0.3s ease;
      opacity: 0;

      /* 图片加载完成后显示 */
      &[style*="width"] {
        opacity: 1;
      }
    }

    .frame-label {
      position: absolute;
      bottom: 8px;
      left: 8px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
    }

    &.fade-out {
      opacity: 0.3;
      transform: scale(0.95);
    }

    &.fade-in {
      opacity: 1;
      transform: scale(1.05);
    }
  }

  .transition-arrow {
    font-size: 24px;
    color: #409eff;
    animation: pulse 2s infinite;
  }
}

.preview-controls {
  display: flex;
  gap: 12px;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 8px rgba(64, 158, 255, 0);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.reference-tags {
  margin-top: 20px;

  .text-info {
    color: #606266;
    font-size: 16px;
    font-weight: 500;
    margin: 10px;
  }

  .tag-container {
    margin: 10px;

    .tag-info {
      margin: 5px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: #409eff;
        color: white;
        transform: translateY(-1px);
      }
    }
  }
}

// 额外的按钮样式修复，确保在紫色背景下可见
.video-size-section {
  // 强制覆盖 Element Plus 的默认样式
  :deep(.el-button--small) {
    font-size: 12px !important;
    line-height: 1.4 !important;

    &.is-plain {
      background-color: rgba(255, 255, 255, 0.1) !important;
      border-color: rgba(255, 255, 255, 0.5) !important;
      color: rgba(255, 255, 255, 0.95) !important;

      &:hover,
      &:focus {
        background-color: rgba(255, 255, 255, 0.2) !important;
        border-color: rgba(255, 255, 255, 0.8) !important;
        color: white !important;
      }
    }
  }

  // 确保按钮内的文本节点颜色正确
  :deep(.el-button span),
  :deep(.el-button) {
    color: rgba(255, 255, 255, 0.95) !important;

    &:hover {
      color: white !important;
    }
  }

  // 针对按钮内容的直接文本节点
  .size-buttons .el-button {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    font-weight: 500;
  }
}

// 全局按钮文字颜色修复（针对紫色背景容器）
.video-size-section .size-buttons {
  // 使用更高优先级的选择器
  .el-button.el-button--small.is-plain {
    color: rgba(255, 255, 255, 0.95) !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;

    &:hover {
      color: white !important;
      background-color: rgba(255, 255, 255, 0.2) !important;
      border-color: rgba(255, 255, 255, 0.8) !important;
    }

    &:focus {
      color: white !important;
      background-color: rgba(255, 255, 255, 0.15) !important;
      border-color: rgba(255, 255, 255, 0.8) !important;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .control-panel {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #dcdfe6;
  }

  .content-display {
    width: 100%;
  }

  .upload-area .upload-grid {
    grid-template-columns: 1fr !important;
  }

  .preview-images {
    flex-direction: column;
    gap: 16px;

    .image-frame {
      min-width: 150px;
      min-height: 120px;
      max-width: 250px;
      max-height: 200px;
    }

    .transition-arrow {
      transform: rotate(90deg);
    }
  }

  .video-actions {
    flex-direction: column;
    width: 100%;

    .el-button {
      width: 100%;
    }
  }
}

// 音频控制样式
.audio-controls {
  .audio-enable {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;

    .audio-label {
      font-size: 14px;
      color: #606266;
    }
  }

  .audio-options {
    padding-left: 16px;
    border-left: 2px solid #e4e7ed;
  }

  .audio-source-selector {
    margin-bottom: 16px;

    .el-radio {
      margin-right: 24px;
    }
  }

  .audio-file-section {
    margin-bottom: 20px;

    .default-audio-info,
    .custom-audio-section {
      margin-top: 12px;
    }

    .audio-file-display {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      background-color: #f5f7fa;
      border-radius: 4px;
      margin-top: 8px;

      .audio-icon {
        color: #409eff;
        font-size: 16px;
      }

      .file-name {
        font-weight: 500;
        color: #303133;
      }

      .file-size,
      .file-status {
        color: #909399;
        font-size: 12px;
      }
    }
  }

  .audio-volume-section {
    margin-bottom: 20px;

    .volume-label {
      font-size: 14px;
      color: #606266;
      margin-bottom: 8px;
    }

    .volume-control {
      .volume-slider {
        width: 100%;
      }
    }
  }

  .audio-tips {
    .tips-list {
      margin: 0;
      padding-left: 16px;

      li {
        margin: 4px 0;
        font-size: 13px;
        color: #606266;
      }
    }
  }
}

// 帮助图标样式
.help-icon {
  margin-left: 4px;
  color: #909399;
  cursor: help;

  &:hover {
    color: #409eff;
  }
}
</style>
