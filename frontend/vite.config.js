import vue from '@vitejs/plugin-vue'
import { defineConfig } from 'vite'
import viteCompression from 'vite-plugin-compression'

import path from 'path'
// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  return {
    // 项目插件
    plugins: [
      vue(),
      viteCompression({
        verbose: true,
        disable: false,
        threshold: 1025,
        algorithm: 'gzip',
        ext: '.gz',
      }),
    ],
    // 基础配置
    base: './',
    publicDir: 'public',
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
      },
    },
    // 开发服务器配置
    server: {
      headers: {
        'Cross-Origin-Opener-Policy': 'same-origin',
        'Cross-Origin-Embedder-Policy': 'require-corp',
        'Content-Security-Policy': "default-src 'self'; connect-src 'self' http://127.0.0.1:18080 https: wss: blob:; style-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; img-src 'self' data: blob:; media-src 'self' blob: data:; object-src 'none'; worker-src 'self' blob:;",
      },
    },
    // 优化配置
    optimizeDeps: {
      exclude: ['@ffmpeg/ffmpeg', '@ffmpeg/util'],
    },
    css: {
      preprocessorOptions: {
        less: {
          modifyVars: {
            // 明亮绿色主题配置
            '@primary-color': '#52c41a',
            '@link-color': '#52c41a',
            '@success-color': '#52c41a',
            '@border-color-base': '#d9d9d9',
            '@component-background': '#ffffff',
            '@body-background': '#f5f5f5',
            '@layout-body-background': '#f0f2f5',
            '@layout-header-background': '#ffffff',
            '@layout-sider-background': '#ffffff',
            '@text-color': 'rgba(0, 0, 0, 0.88)',
            '@heading-color': 'rgba(0, 0, 0, 0.88)',
            '@text-color-secondary': 'rgba(0, 0, 0, 0.65)',
          },
          javascriptEnabled: true,
        },
        scss: { api: 'modern-compiler' }
      },
    },
    build: {
      outDir: 'dist',
      assetsDir: 'assets',
      assetsInlineLimit: 4096,
      cssCodeSplit: true,
      brotliSize: false,
      sourcemap: false,
      minify: 'terser',
      terserOptions: {
        compress: {
          // 生产环境去除console及debug
          drop_console: false,
          drop_debugger: true,
        },
      },
    },
  }
})


