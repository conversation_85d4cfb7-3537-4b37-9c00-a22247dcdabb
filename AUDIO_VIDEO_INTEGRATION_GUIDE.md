# FFmpeg.js 音频视频集成完整指南

## 功能概述

本次更新为 FFmpeg.js 视频生成功能添加了完整的背景音乐支持，包括：

- ✅ 内置默认背景音乐 (`mp1.mp3`)
- ✅ 自定义音频文件上传
- ✅ 音频长度自动调整（循环或截取）
- ✅ 音量控制
- ✅ 多种音频格式支持
- ✅ Electron 和浏览器环境兼容
- ✅ 错误处理和降级方案

## 核心修改内容

### 1. FFmpeg Helper 音频支持 (`frontend/src/utils/ffmpegHelper.js`)

#### 新增方法：
- `prepareAudioFile()` - 音频文件准备和处理
- `loadDefaultAudio()` - 加载默认音频文件
- `loadAudioInElectron()` - Electron 环境音频加载
- `loadAudioInBrowser()` - 浏览器环境音频加载
- `getAudioInfo()` - 获取音频信息
- `adjustAudioLength()` - 调整音频长度

#### 修改方法：
- `generateTransitionVideo()` - 支持音频参数
- `buildFFmpegCommand()` - 支持音频处理的 FFmpeg 命令

#### 关键特性：
```javascript
// 音频长度调整
if (audioDuration <= targetDuration) {
  // 音频比视频短，循环播放
  const loopCount = Math.ceil(targetDuration / audioDuration)
  await this.ffmpeg.exec([
    '-stream_loop', (loopCount - 1).toString(),
    '-i', inputAudio,
    '-t', targetDuration.toString(),
    '-c:a', 'aac',
    '-b:a', '128k',
    outputAudio
  ])
} else {
  // 音频比视频长，截取前段
  await this.ffmpeg.exec([
    '-i', inputAudio,
    '-t', targetDuration.toString(),
    '-c:a', 'aac',
    '-b:a', '128k',
    outputAudio
  ])
}
```

### 2. Electron 音频文件访问 (`electron/controller/file_dialog.js`)

#### 新增方法：
- `getAssetPath()` - 获取应用资源文件路径

#### 功能：
- 开发环境和生产环境路径处理
- 多路径尝试机制
- 文件存在性验证

### 3. 用户界面增强 (`frontend/src/views/genimgauto/genVideo.vue`)

#### 新增功能：
- 背景音乐开关
- 音频源选择（默认/自定义）
- 音量控制滑块
- 音频文件信息显示
- 音频处理状态提示

#### 新增方法：
- `selectAudioFile()` - 选择音频文件
- `removeAudioFile()` - 移除音频文件
- `getAudioDuration()` - 获取音频时长
- `formatVolumeTooltip()` - 格式化音量提示

## FFmpeg 命令结构

### 无音频视频：
```bash
ffmpeg -loop 1 -i start.jpg -loop 1 -i end.jpg \
  -filter_complex "[0:v]scale=1280:720[v0];[1:v]scale=1280:720[v1];[v0][v1]blend=all_expr='A*(1-T/5)+B*(T/5)':shortest=1[video]" \
  -map "[video]" -t 5 -r 30 -c:v libx264 -pix_fmt yuv420p output.mp4
```

### 带音频视频：
```bash
ffmpeg -loop 1 -i start.jpg -loop 1 -i end.jpg -i audio_adjusted.mp3 \
  -filter_complex "[0:v]scale=1280:720[v0];[1:v]scale=1280:720[v1];[v0][v1]blend=all_expr='A*(1-T/5)+B*(T/5)':shortest=1[video];[2:a]volume=0.5[audio]" \
  -map "[video]" -map "[audio]" -t 5 -r 30 -c:v libx264 -pix_fmt yuv420p -c:a aac -b:a 128k -ar 44100 output.mp4
```

## 音频处理流程

### 1. 音频文件加载
```javascript
// 默认音频（浏览器环境）
const audioModule = await import('@/assets/mp1.mp3')
const audioData = await fetchFile(audioModule.default)

// 默认音频（Electron 环境）
const audioPath = await window.electron.ipcRenderer.invoke('app:getAssetPath', 'mp1.mp3')
const audioData = await window.electron.ipcRenderer.invoke('fs:readFile', audioPath)
```

### 2. 音频长度调整
```javascript
// 循环播放（音频短于视频）
if (audioDuration <= targetDuration) {
  const loopCount = Math.ceil(targetDuration / audioDuration)
  // 使用 -stream_loop 参数循环播放
}

// 截取音频（音频长于视频）
else {
  // 使用 -t 参数截取指定时长
}
```

### 3. 音频编码参数
- **编码器**: AAC (`-c:a aac`)
- **比特率**: 128k (`-b:a 128k`)
- **采样率**: 44100Hz (`-ar 44100`)
- **音量控制**: volume 滤镜 (`[2:a]volume=0.5[audio]`)

## 支持的音频格式

### 输入格式：
- MP3 (推荐)
- WAV
- AAC
- M4A
- OGG

### 输出格式：
- AAC (统一输出格式)

## 使用方法

### 1. 基本使用
```javascript
import ffmpegHelper from '@/utils/ffmpegHelper'

const result = await ffmpegHelper.generateTransitionVideo(
  startImage,
  endImage,
  {
    duration: 5,
    effect: 'crossfade',
    enableAudio: true,
    audioVolume: 0.5
  }
)
```

### 2. 自定义音频
```javascript
const result = await ffmpegHelper.generateTransitionVideo(
  startImage,
  endImage,
  {
    duration: 5,
    effect: 'fade',
    enableAudio: true,
    audioFile: customAudioFile,
    audioVolume: 0.8
  }
)
```

### 3. 测试组件使用
```vue
<template>
  <AudioVideoTest />
</template>

<script>
import AudioVideoTest from '@/components/AudioVideoTest.vue'

export default {
  components: {
    AudioVideoTest
  }
}
</script>
```

## 错误处理

### 1. 音频加载失败
```javascript
// 自动降级到无音频视频
if (!audioData) {
  console.warn('音频文件加载失败，将生成无音频视频')
  return false
}
```

### 2. 音频处理失败
```javascript
// 继续生成视频，但不包含音频
catch (error) {
  console.error('音频处理失败:', error)
  if (this.progressCallback) {
    this.progressCallback({
      type: 'status',
      message: '音频处理失败，将生成无音频视频'
    })
  }
  return false
}
```

### 3. 文件格式不支持
```javascript
// 验证文件类型
const allowedTypes = ['audio/mpeg', 'audio/wav', 'audio/aac', 'audio/mp4', 'audio/ogg']
if (!allowedTypes.includes(file.type)) {
  ElMessage.error('请选择支持的音频格式（MP3、WAV、AAC、M4A、OGG）')
  return
}
```

## 性能优化

### 1. 音频文件大小限制
```javascript
// 限制音频文件大小为50MB
const maxSize = 50 * 1024 * 1024
if (file.size > maxSize) {
  ElMessage.error('音频文件大小不能超过50MB')
  return
}
```

### 2. 音频编码优化
```javascript
// 使用适中的音频比特率
'-c:a', 'aac',
'-b:a', '128k',  // 128kbps 提供良好的质量和文件大小平衡
'-ar', '44100'   // 标准采样率
```

### 3. 内存管理
```javascript
// 及时释放音频URL对象
URL.revokeObjectURL(audioUrl)

// 清理FFmpeg临时文件
await this.cleanup()
```

## 兼容性说明

### 浏览器环境：
- ✅ Chrome 67+
- ✅ Firefox 79+
- ✅ Edge 79+
- ⚠️ Safari（部分限制）

### Electron 环境：
- ✅ Electron 10.0+
- ✅ 所有主要操作系统
- ✅ 开发和生产环境

### 音频格式兼容性：
- ✅ MP3（最佳兼容性）
- ✅ WAV（无损，文件较大）
- ✅ AAC（现代格式）
- ✅ M4A（Apple 格式）
- ⚠️ OGG（部分浏览器支持）

## 故障排除

### 1. 默认音频不可用
**症状**：提示"默认音频不可用"
**解决方案**：
- 确保 `frontend/src/assets/mp1.mp3` 文件存在
- 检查文件路径配置
- 验证 Webpack 资源加载配置

### 2. 自定义音频加载失败
**症状**：选择音频文件后处理失败
**解决方案**：
- 检查文件格式是否支持
- 验证文件大小是否超限
- 确保文件未损坏

### 3. 音频视频不同步
**症状**：生成的视频中音频和视频不匹配
**解决方案**：
- 检查音频长度调整逻辑
- 验证 FFmpeg 命令参数
- 确保音频和视频时长一致

## 更新日志

- **v1.0.0**: 基础音频支持
- **v1.1.0**: 添加音量控制和格式验证
- **v1.2.0**: 增强 Electron 环境支持
- **v1.3.0**: 添加音频长度自动调整功能
